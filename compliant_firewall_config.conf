# 合规的防火墙配置文件示例
# 符合技术规范要求的配置

# 清空现有规则
-F
-X
-Z

# 设置默认策略
-P INPUT DROP
-P FORWARD DROP
-P OUTPUT ACCEPT

# 允许本地回环
-A INPUT -i lo -j ACCEPT
-A OUTPUT -o lo -j ACCEPT

# 允许已建立的连接
-A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# ========== 管理访问规则（符合规范）==========

# SSH访问仅限管理网段
-A INPUT -s ************/24 -d ************ -p tcp --dport 22 -j ACCEPT

# HTTPS管理界面仅限管理员IP
-A INPUT -s ************ -d ************ -p tcp --dport 443 -j ACCEPT

# ========== 业务访问规则（符合规范）==========

# Web服务访问 - 限制源网段和目标IP
-A INPUT -s ***********/24 -d ************** -p tcp --dport 80 -j ACCEPT
-A INPUT -s ***********/24 -d ************** -p tcp --dport 443 -j ACCEPT

# 应用服务器访问数据库 - 精确的点对点访问
-A INPUT -s ************ -d ************ -p tcp --dport 3306 -j ACCEPT
-A INPUT -s ************ -d ************ -p tcp --dport 3306 -j ACCEPT

# API服务访问 - 限制源IP和目标IP
-A INPUT -s ************ -d ************0 -p tcp --dport 8080 -j ACCEPT
-A INPUT -s ************ -d ************0 -p tcp --dport 8080 -j ACCEPT

# ========== DMZ区访问控制（符合规范）==========

# 外联单位到DMZ前置主机 - 端口级访问控制
-A FORWARD -i wan0 -o dmz0 -s ************* -d ************** -p tcp --dport 80 -j ACCEPT
-A FORWARD -i wan0 -o dmz0 -s ************* -d ************** -p tcp --dport 443 -j ACCEPT
-A FORWARD -i wan0 -o dmz0 -s ************* -d ************** -p tcp --dport 8080 -j ACCEPT

# DMZ前置主机到内网业务服务器 - 端口级访问控制
-A FORWARD -i dmz0 -o lan0 -s ************** -d ************ -p tcp --dport 3306 -j ACCEPT
-A FORWARD -i dmz0 -o lan0 -s ************** -d ************ -p tcp --dport 5432 -j ACCEPT
-A FORWARD -i dmz0 -o lan0 -s ************** -d ************ -p tcp --dport 6379 -j ACCEPT

# ========== 系统服务访问（符合规范）==========

# DNS查询 - 限制源网段
-A INPUT -s ***********/24 -d *********** -p udp --dport 53 -j ACCEPT
-A INPUT -s ***********/24 -d *********** -p udp --dport 53 -j ACCEPT

# NTP时间同步 - 限制源网段和目标
-A INPUT -s ***********/24 -d *********** -p udp --dport 123 -j ACCEPT

# ========== 监控和日志访问（符合规范）==========

# 监控系统访问 - 限制源IP和目标IP
-A INPUT -s ************* -d ************ -p tcp --dport 9100 -j ACCEPT
-A INPUT -s ************* -d ************ -p tcp --dport 9090 -j ACCEPT

# 日志收集 - 限制源网段和目标
-A INPUT -s ***********/24 -d ************* -p tcp --dport 514 -j ACCEPT
-A INPUT -s ***********/24 -d ************* -p udp --dport 514 -j ACCEPT

# ========== 备份和维护访问（符合规范）==========

# 备份服务访问 - 限制源IP和目标IP
-A INPUT -s ************0 -d ************* -p tcp --dport 22 -j ACCEPT
-A INPUT -s ************0 -d ************* -p tcp --dport 873 -j ACCEPT

# ========== ICMP控制（符合规范）==========

# 允许内网ping - 限制源网段
-A INPUT -s ***********/24 -p icmp --icmp-type echo-request -j ACCEPT
-A INPUT -s ***********/24 -p icmp --icmp-type echo-request -j ACCEPT

# ========== 安全加固规则 ==========

# 防止端口扫描
-A INPUT -p tcp --tcp-flags ALL NONE -j DROP
-A INPUT -p tcp --tcp-flags ALL ALL -j DROP
-A INPUT -p tcp --tcp-flags ALL FIN,URG,PSH -j DROP
-A INPUT -p tcp --tcp-flags ALL SYN,RST,ACK,FIN,URG -j DROP

# 限制连接频率
-A INPUT -p tcp --dport 80 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT
-A INPUT -p tcp --dport 443 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT

# ========== 日志记录 ==========

# 记录被拒绝的连接（限制日志频率）
-A INPUT -m limit --limit 5/min -j LOG --log-prefix "INPUT_DENIED: " --log-level 7
-A FORWARD -m limit --limit 5/min -j LOG --log-prefix "FORWARD_DENIED: " --log-level 7

# ========== 最终拒绝规则 ==========

# 拒绝所有其他连接
-A INPUT -j DROP
-A FORWARD -j DROP
