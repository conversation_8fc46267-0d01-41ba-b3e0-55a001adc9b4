#!/usr/bin/env python3
"""
防火墙分析工具演示脚本
展示工具的主要功能
"""

from firewall_analyzer import FirewallAnalyzer
import json


def demo_firewall_analyzer():
    """演示防火墙分析工具的功能"""
    print("=" * 60)
    print("网络设备防火墙配置分析工具演示")
    print("=" * 60)
    
    # 创建分析器
    analyzer = FirewallAnalyzer()
    
    # 示例iptables配置
    sample_config = """
# 示例防火墙配置
-A INPUT -i lo -j ACCEPT
-A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
-A INPUT -p tcp --dport 22 -j ACCEPT
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -p tcp --dport 443 -j ACCEPT
-A INPUT -s ************* -p tcp --dport 3306 -j ACCEPT
-A INPUT -p tcp --dport 3306 -j DROP
-A INPUT -p udp --dport 53 -j ACCEPT
-A INPUT -p icmp --icmp-type echo-request -j ACCEPT
-A INPUT -j DROP
"""
    
    print("\n1. 解析防火墙配置")
    print("-" * 30)
    if analyzer.parse_config(sample_config):
        print("✓ 配置解析成功")
        print(f"✓ 检测到配置类型: {analyzer.config_type}")
        print(f"✓ 解析出 {len(analyzer.rules)} 条规则")
    else:
        print("✗ 配置解析失败")
        return
    
    print("\n2. 配置摘要信息")
    print("-" * 30)
    summary = analyzer.get_summary()
    print(f"配置类型: {summary['config_type']}")
    print(f"总规则数: {summary['total_rules']}")
    print(f"允许规则: {summary['allow_rules']}")
    print(f"拒绝规则: {summary['deny_rules']}")
    
    print("\n3. 详细规则列表")
    print("-" * 30)
    for i, rule_str in enumerate(summary['rules'], 1):
        print(f"{i:2d}. {rule_str}")
    
    print("\n4. 端口访问检查测试")
    print("-" * 30)
    
    # 测试用例
    test_cases = [
        ("************", 22, "tcp", "SSH访问"),
        ("********", 80, "tcp", "HTTP访问"),
        ("*******", 443, "tcp", "HTTPS访问"),
        ("*************", 3306, "tcp", "MySQL访问(允许IP)"),
        ("*************", 3306, "tcp", "MySQL访问(其他IP)"),
        ("*******", 53, "udp", "DNS查询"),
        ("************", 8080, "tcp", "未配置端口"),
        ("************", 25, "tcp", "SMTP端口"),
    ]
    
    for ip, port, protocol, description in test_cases:
        result = analyzer.check_port_access(ip, port, protocol)
        status = "🔴 被阻止" if result["is_blocked"] else "🟢 允许"
        print(f"{description:20s} ({ip}:{port}/{protocol}): {status}")
        
        # 显示详细分析
        if result["analysis"]:
            print(f"                     分析: {result['analysis']}")
        
        # 显示相关规则
        if result["allowing_rules"]:
            print(f"                     允许规则: {len(result['allowing_rules'])} 条")
        if result["blocking_rules"]:
            print(f"                     阻止规则: {len(result['blocking_rules'])} 条")
        print()
    
    print("\n5. 特定场景分析")
    print("-" * 30)
    
    # 分析MySQL访问控制
    print("MySQL访问控制分析:")
    mysql_allowed = analyzer.check_port_access("*************", 3306, "tcp")
    mysql_blocked = analyzer.check_port_access("*************", 3306, "tcp")
    
    print(f"  - *************访问MySQL: {'允许' if not mysql_allowed['is_blocked'] else '拒绝'}")
    print(f"  - *************访问MySQL: {'允许' if not mysql_blocked['is_blocked'] else '拒绝'}")
    print("  - 结论: MySQL仅允许特定IP访问，符合安全最佳实践")
    
    # 分析Web服务访问
    print("\nWeb服务访问分析:")
    http_result = analyzer.check_port_access("***********", 80, "tcp")
    https_result = analyzer.check_port_access("***********", 443, "tcp")
    
    print(f"  - HTTP (80端口): {'开放' if not http_result['is_blocked'] else '关闭'}")
    print(f"  - HTTPS (443端口): {'开放' if not https_result['is_blocked'] else '关闭'}")
    
    print("\n6. 安全建议")
    print("-" * 30)
    
    # 基于规则分析提供安全建议
    suggestions = []
    
    # 检查是否有过于宽松的规则
    open_ports = []
    for rule in analyzer.rules:
        if (rule.action.value == "allow" and 
            rule.source_ip == "any" and 
            rule.dest_port not in ["22", "80", "443", "53", "any"]):
            open_ports.append(rule.dest_port)
    
    if open_ports:
        suggestions.append(f"发现对所有IP开放的端口: {', '.join(open_ports)}，建议限制源IP范围")
    
    # 检查SSH访问
    ssh_rules = [r for r in analyzer.rules if r.dest_port == "22" and r.action.value == "allow"]
    if ssh_rules and any(r.source_ip == "any" for r in ssh_rules):
        suggestions.append("SSH端口对所有IP开放，建议限制管理IP范围")
    
    # 检查默认拒绝策略
    drop_rules = [r for r in analyzer.rules if r.action.value in ["drop", "deny"]]
    if not drop_rules:
        suggestions.append("建议添加默认拒绝规则以提高安全性")
    
    if suggestions:
        for i, suggestion in enumerate(suggestions, 1):
            print(f"{i}. {suggestion}")
    else:
        print("✓ 当前配置符合基本安全要求")
    
    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)


if __name__ == "__main__":
    try:
        demo_firewall_analyzer()
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
