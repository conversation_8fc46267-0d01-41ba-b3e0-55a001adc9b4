#!/usr/bin/env python3
"""
网络设备配置安全分析工具测试脚本
"""

import unittest
import tempfile
import os
from network_security_analyzer import NetworkSecurityAnalyzer, ComplianceLevel


class TestNetworkSecurityAnalyzer(unittest.TestCase):
    """网络安全分析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.analyzer = NetworkSecurityAnalyzer()
    
    def test_parse_iptables_config(self):
        """测试iptables配置解析"""
        test_config = """
-A INPUT -i lo -j ACCEPT
-A INPUT -p tcp --dport 22 -j ACCEPT
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -s ************* -p tcp --dport 3306 -j ACCEPT
-A INPUT -j DROP
"""
        result = self.analyzer.parse_config(test_config)
        self.assertTrue(result)
        self.assertEqual(self.analyzer.config_type, "iptables")
        self.assertGreater(len(self.analyzer.rules), 0)
    
    def test_management_port_detection(self):
        """测试管理端口检测"""
        test_config = """
-A INPUT -p tcp --dport 22 -j ACCEPT
-A INPUT -p tcp --dport 23 -j ACCEPT
-A INPUT -p tcp --dport 3389 -j ACCEPT
"""
        self.analyzer.parse_config(test_config)
        report = self.analyzer.check_compliance()
        
        # 应该检测到管理端口违规
        mgmt_issues = [issue for issue in report['issues'] if issue['category'] == "管理端口安全"]
        self.assertGreater(len(mgmt_issues), 0)
        
        # 检查是否正确识别为违规
        violations = [issue for issue in mgmt_issues if issue['level'] == "violation"]
        self.assertGreater(len(violations), 0)
    
    def test_any_ip_detection(self):
        """测试any IP检测"""
        test_config = """
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -s ***********/24 -p tcp --dport 8080 -j ACCEPT
"""
        self.analyzer.parse_config(test_config)
        report = self.analyzer.check_compliance()
        
        # 应该检测到any IP违规
        ip_issues = [issue for issue in report['issues'] if issue['category'] == "IP地址规范"]
        self.assertGreater(len(ip_issues), 0)
    
    def test_compliant_config(self):
        """测试合规配置"""
        test_config = """
-A INPUT -s ************* -d ************* -p tcp --dport 8080 -j ACCEPT
-A INPUT -s ***********/24 -d ************* -p tcp --dport 443 -j ACCEPT
"""
        self.analyzer.parse_config(test_config)
        report = self.analyzer.check_compliance()
        
        # 应该没有违规项
        violations = [issue for issue in report['issues'] if issue['level'] == "violation"]
        self.assertEqual(len(violations), 0)
    
    def test_cisco_acl_parsing(self):
        """测试Cisco ACL解析"""
        test_config = """
access-list 100 permit tcp *********** ********* any eq 80
access-list 100 deny tcp any any eq 22
"""
        result = self.analyzer.parse_config(test_config)
        self.assertTrue(result)
        self.assertEqual(self.analyzer.config_type, "cisco_acl")
    
    def test_generic_rule_parsing(self):
        """测试通用规则解析"""
        test_config = """
allow tcp from ***********/24 to ************* port 80
deny tcp from any to any port 22
"""
        result = self.analyzer.parse_config(test_config)
        self.assertTrue(result)
        self.assertEqual(self.analyzer.config_type, "generic")
    
    def test_load_config_from_file(self):
        """测试从文件加载配置"""
        test_config = """
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -j DROP
"""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.conf') as f:
            f.write(test_config)
            temp_file = f.name
        
        try:
            result = self.analyzer.load_config_from_file(temp_file)
            self.assertTrue(result)
            self.assertGreater(len(self.analyzer.rules), 0)
        finally:
            os.unlink(temp_file)
    
    def test_compliance_report_structure(self):
        """测试合规性报告结构"""
        test_config = """
-A INPUT -p tcp --dport 22 -j ACCEPT
"""
        self.analyzer.parse_config(test_config)
        report = self.analyzer.check_compliance()
        
        # 检查报告结构
        self.assertIn('config_type', report)
        self.assertIn('total_rules', report)
        self.assertIn('compliance_status', report)
        self.assertIn('summary', report)
        self.assertIn('categories', report)
        self.assertIn('issues', report)
        
        # 检查摘要结构
        self.assertIn('violations', report['summary'])
        self.assertIn('warnings', report['summary'])
        self.assertIn('total_issues', report['summary'])
        
        # 检查分类结构
        self.assertIn('management_ports', report['categories'])
        self.assertIn('any_ip_usage', report['categories'])
        self.assertIn('access_control', report['categories'])


def run_manual_tests():
    """运行手动测试"""
    print("=" * 60)
    print("网络设备配置安全分析工具手动测试")
    print("=" * 60)
    
    analyzer = NetworkSecurityAnalyzer()
    
    # 测试1: 管理端口检测
    print("\n1. 测试管理端口检测")
    print("-" * 30)
    
    mgmt_test_config = """
-A INPUT -p tcp --dport 22 -j ACCEPT
-A INPUT -s ************* -p tcp --dport 22 -j ACCEPT
-A INPUT -p tcp --dport 23 -j ACCEPT
-A INPUT -p tcp --dport 3389 -j ACCEPT
"""
    
    if analyzer.parse_config(mgmt_test_config):
        report = analyzer.check_compliance()
        mgmt_issues = [issue for issue in report['issues'] if issue['category'] == "管理端口安全"]
        print(f"检测到管理端口问题: {len(mgmt_issues)} 项")
        for issue in mgmt_issues:
            level = "违规" if issue['level'] == "violation" else "警告"
            print(f"  - [{level}] {issue['description']}")
    
    # 测试2: any IP检测
    print("\n2. 测试any IP检测")
    print("-" * 30)
    
    analyzer2 = NetworkSecurityAnalyzer()
    any_ip_test_config = """
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -s ***********/24 -p tcp --dport 8080 -j ACCEPT
-A FORWARD -j ACCEPT
"""
    
    if analyzer2.parse_config(any_ip_test_config):
        report = analyzer2.check_compliance()
        ip_issues = [issue for issue in report['issues'] if issue['category'] == "IP地址规范"]
        access_issues = [issue for issue in report['issues'] if issue['category'] == "访问控制"]
        
        print(f"检测到IP地址规范问题: {len(ip_issues)} 项")
        for issue in ip_issues:
            print(f"  - {issue['description']}")
        
        print(f"检测到访问控制问题: {len(access_issues)} 项")
        for issue in access_issues:
            print(f"  - {issue['description']}")
    
    # 测试3: 合规配置
    print("\n3. 测试合规配置")
    print("-" * 30)
    
    analyzer3 = NetworkSecurityAnalyzer()
    compliant_config = """
-A INPUT -s ************* -d ************* -p tcp --dport 8080 -j ACCEPT
-A INPUT -s ***********/24 -d ************* -p tcp --dport 443 -j ACCEPT
-A INPUT -s ************ -d ************ -p tcp --dport 22 -j ACCEPT
"""
    
    if analyzer3.parse_config(compliant_config):
        report = analyzer3.check_compliance()
        print(f"合规状态: {report['compliance_status']}")
        print(f"发现问题: {report['summary']['total_issues']} 项")
        
        if report['issues']:
            for issue in report['issues']:
                level = "违规" if issue['level'] == "violation" else "警告"
                print(f"  - [{level}] {issue['description']}")
        else:
            print("✅ 配置完全符合规范要求")
    
    print("\n" + "=" * 60)
    print("手动测试完成")
    print("=" * 60)


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 运行单元测试")
    print("2. 运行手动测试")
    print("3. 运行所有测试")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        unittest.main(argv=[''], exit=False, verbosity=2)
    elif choice == "2":
        run_manual_tests()
    elif choice == "3":
        print("=== 运行单元测试 ===")
        unittest.main(argv=[''], exit=False, verbosity=2)
        print("\n" + "="*50 + "\n")
        run_manual_tests()
    else:
        print("无效选择，运行手动测试...")
        run_manual_tests()
