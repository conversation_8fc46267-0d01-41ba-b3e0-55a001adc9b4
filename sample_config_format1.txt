# 防火墙配置文件 - 格式1 (通用格式)
# 文件格式: allow/deny protocol from source_ip to dest_ip port port_number

# 管理访问规则
allow tcp from ************/24 to ***********0 port 22
allow tcp from ************ to ***********0 port 443

# Web服务访问
allow tcp from ***********/24 to ************** port 80
allow tcp from ***********/24 to ************** port 443

# 数据库访问
allow tcp from ************ to ************ port 3306
allow tcp from ************ to ************ port 3306

# 违规示例 - 管理端口对any开放
allow tcp from any to any port 22
allow tcp from any to any port 23
allow tcp from any to any port 3389

# 违规示例 - any IP设置
allow tcp from any to ***********00 port 80
allow tcp from ***********/24 to any port 8080

# DNS服务
allow udp from ***********/24 to *********** port 53

# 默认拒绝
deny tcp from any to any port any
