# 示例防火墙配置文件 - 包含合规性问题用于测试
# 这个配置文件故意包含一些违反技术规范的规则

# 清空现有规则
-F
-X
-Z

# 设置默认策略
-P INPUT DROP
-P FORWARD DROP
-P OUTPUT ACCEPT

# 允许本地回环
-A INPUT -i lo -j ACCEPT
-A OUTPUT -o lo -j ACCEPT

# 允许已建立的连接
-A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# ========== 违规规则示例 ==========

# 违规1: SSH端口对any开放（违反管理端口安全规范）
-A INPUT -p tcp --dport 22 -j ACCEPT

# 违规2: Telnet端口对any开放（违反管理端口安全规范）
-A INPUT -p tcp --dport 23 -j ACCEPT

# 违规3: RDP端口对any开放（违反管理端口安全规范）
-A INPUT -p tcp --dport 3389 -j ACCEPT

# 违规4: 源IP和目标IP都设置为any（违反IP地址规范）
-A INPUT -p tcp --dport 80 -j ACCEPT

# 违规5: 允许任意源到任意目标的任意端口（违反访问控制规范）
-A FORWARD -j ACCEPT

# ========== 部分合规规则示例 ==========

# 警告: SSH端口开放但限制了源IP（需要确认业务需求）
-A INPUT -s ************* -p tcp --dport 22 -j ACCEPT

# 合规: HTTPS端口开放且限制了源IP段
-A INPUT -s ***********/24 -p tcp --dport 443 -j ACCEPT

# 合规: 特定业务端口开放且限制了源IP和目标IP
-A INPUT -s ************ -d ************* -p tcp --dport 8080 -j ACCEPT

# 合规: 数据库端口仅允许特定应用服务器访问
-A INPUT -s ************ -d ************ -p tcp --dport 3306 -j ACCEPT

# 合规: DNS查询限制源IP
-A INPUT -s ***********/24 -p udp --dport 53 -j ACCEPT

# ========== 更多违规示例 ==========

# 违规6: SNMP端口对any开放
-A INPUT -p udp --dport 161 -j ACCEPT

# 违规7: VNC端口对any开放
-A INPUT -p tcp --dport 5900 -j ACCEPT

# 违规8: FTP端口对any开放
-A INPUT -p tcp --dport 21 -j ACCEPT

# ========== DMZ区相关规则 ==========

# 违规9: DMZ到内网的访问过于宽泛
-A FORWARD -i dmz0 -o lan0 -j ACCEPT

# 合规: 限制DMZ到内网的特定服务访问
-A FORWARD -i dmz0 -o lan0 -s ************** -d ************ -p tcp --dport 3306 -j ACCEPT

# 违规10: 外网到DMZ的访问设置为any
-A FORWARD -i wan0 -o dmz0 -p tcp --dport 80 -j ACCEPT

# 合规: 限制外网到DMZ的访问源
-A FORWARD -i wan0 -o dmz0 -s ***********/24 -d ************** -p tcp --dport 80 -j ACCEPT

# ========== 日志和最终拒绝规则 ==========

# 记录被拒绝的连接
-A INPUT -m limit --limit 5/min -j LOG --log-prefix "iptables denied: " --log-level 7
-A FORWARD -m limit --limit 5/min -j LOG --log-prefix "iptables forward denied: " --log-level 7

# 最终拒绝规则
-A INPUT -j DROP
-A FORWARD -j DROP
