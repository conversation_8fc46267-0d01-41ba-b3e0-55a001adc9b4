#!/usr/bin/env python3
"""
TXT格式配置文件导入和分析演示脚本
展示工具对各种TXT格式的支持能力
"""

from network_security_analyzer import NetworkSecurityAnalyzer
import tempfile
import os


def create_demo_txt_files():
    """创建演示用的TXT配置文件"""
    
    # 创建一个包含各种格式的混合TXT文件
    mixed_format_content = """# 混合格式防火墙配置文件演示
# 这个文件包含多种TXT格式，用于演示工具的格式识别能力

# ========== 通用格式规则 ==========
# 管理访问 - 合规示例
allow tcp from ************/24 to ***********0 port 22
allow tcp from 192.168.10.5 to ***********0 port 443

# Web服务访问 - 合规示例
allow tcp from ***********/24 to ************** port 80
allow tcp from ***********/24 to ************** port 443

# ========== 箭头格式规则 ==========
# 数据库访问 - 合规示例
192.168.2.10 -> ************:3306 TCP ALLOW
************ -> ************:3306 TCP ALLOW

# ========== 表格格式规则 ==========
# DNS服务 - 合规示例
UDP 53 ***********/24 *********** ALLOW

# ========== 动作在前格式规则 ==========
# 监控服务 - 合规示例
ALLOW TCP ************* ***********0 9100

# ========== 违规示例 ==========
# 管理端口对any开放 - 严重违规
allow tcp from any to any port 22
allow tcp from any to any port 23
***********00 -> any:3389 TCP ALLOW
TCP 21 any any ALLOW
ALLOW TCP any any 161

# any IP设置 - 违规
allow tcp from any to ***********00 port 80
***********/24 -> any:8080 TCP ALLOW
TCP 443 any ************* ALLOW

# 过于宽泛的访问控制 - 违规
allow tcp from any to any port any

# ========== iptables格式混合 ==========
-A INPUT -i lo -j ACCEPT
-A INPUT -p tcp --dport 443 -s ***********/24 -j ACCEPT

# ========== 默认拒绝规则 ==========
deny tcp from any to any port any
deny udp from any to any port any
"""
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(mixed_format_content)
        return f.name


def demo_txt_format_analysis():
    """演示TXT格式分析功能"""
    print("=" * 80)
    print("TXT格式配置文件导入和分析演示")
    print("=" * 80)
    
    # 创建演示文件
    demo_file = create_demo_txt_files()
    
    try:
        print(f"\n1. 创建混合格式演示文件")
        print(f"   文件路径: {demo_file}")
        print(f"   文件包含多种TXT格式规则和违规示例")
        
        # 创建分析器
        analyzer = NetworkSecurityAnalyzer()
        
        print(f"\n2. 加载和解析TXT配置文件")
        print("-" * 50)
        
        if analyzer.load_config_from_file(demo_file):
            print(f"✅ 成功加载TXT配置文件")
            print(f"   配置类型: {analyzer.config_type}")
            print(f"   解析规则数: {len(analyzer.rules)}")
        else:
            print(f"❌ TXT配置文件加载失败")
            return
        
        print(f"\n3. 显示解析的规则")
        print("-" * 50)
        
        for i, rule in enumerate(analyzer.rules, 1):
            print(f"{i:2d}. {rule}")
        
        print(f"\n4. 执行合规性检查")
        print("-" * 50)
        
        report = analyzer.check_compliance()
        
        print(f"合规状态: {report['compliance_status']}")
        print(f"问题总数: {report['summary']['total_issues']}")
        print(f"严重违规: {report['summary']['violations']} 项")
        print(f"警告事项: {report['summary']['warnings']} 项")
        
        print(f"\n5. 问题分类统计")
        print("-" * 50)
        print(f"管理端口安全问题: {report['categories']['management_ports']} 项")
        print(f"IP地址规范问题: {report['categories']['any_ip_usage']} 项")
        print(f"访问控制问题: {report['categories']['access_control']} 项")
        
        print(f"\n6. 详细问题列表")
        print("-" * 50)
        
        if report['issues']:
            for i, issue in enumerate(report['issues'], 1):
                level_symbol = "🔴" if issue['level'] == "violation" else "🟡"
                print(f"{i:2d}. {level_symbol} [{issue['level'].upper()}] {issue['category']}")
                print(f"     问题: {issue['description']}")
                print(f"     建议: {issue['recommendation']}")
                if issue['rule_details']:
                    print(f"     规则: {issue['rule_details']}")
                print()
        else:
            print("✅ 未发现合规性问题")
        
        print(f"\n7. 生成文本格式报告")
        print("-" * 50)
        
        text_report = analyzer._format_text_report(report)
        
        # 保存报告到文件
        report_file = "txt_format_analysis_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(text_report)
        
        print(f"✅ 详细报告已保存到: {report_file}")
        
    finally:
        # 清理临时文件
        if os.path.exists(demo_file):
            os.remove(demo_file)


def demo_format_comparison():
    """演示不同格式的对比"""
    print(f"\n\n{'='*80}")
    print("不同TXT格式对比演示")
    print(f"{'='*80}")
    
    # 同一规则的不同格式表示
    rule_formats = [
        ("通用格式", "allow tcp from ***********/24 to ************* port 80"),
        ("箭头格式", "***********/24 -> *************:80 TCP ALLOW"),
        ("表格格式", "TCP 80 ***********/24 ************* ALLOW"),
        ("动作在前", "ALLOW TCP ***********/24 ************* 80"),
        ("简单格式", "***********/24 ************* 80 TCP ALLOW"),
        ("iptables", "-A INPUT -s ***********/24 -d ************* -p tcp --dport 80 -j ACCEPT")
    ]
    
    print(f"\n同一规则的不同格式表示:")
    print(f"规则含义: 允许***********/24网段访问*************的80端口")
    print("-" * 60)
    
    for format_name, rule_text in rule_formats:
        print(f"{format_name:12s}: {rule_text}")
    
    print(f"\n格式转换和解析测试:")
    print("-" * 60)
    
    analyzer = NetworkSecurityAnalyzer()
    
    for format_name, rule_text in rule_formats:
        print(f"\n{format_name}:")
        print(f"  原始: {rule_text}")
        
        # 创建测试配置
        test_config = f"# 测试\n{rule_text}"
        
        # 测试解析
        test_analyzer = NetworkSecurityAnalyzer()
        if test_analyzer.parse_config(test_config):
            if test_analyzer.rules:
                rule = test_analyzer.rules[0]
                print(f"  解析: ✅ 成功")
                print(f"  结果: {rule.action.value} {rule.protocol.value} {rule.source_ip}:{rule.source_port} -> {rule.dest_ip}:{rule.dest_port}")
            else:
                print(f"  解析: ⚠️ 无规则生成")
        else:
            print(f"  解析: ❌ 失败")


def demo_encoding_support():
    """演示编码支持"""
    print(f"\n\n{'='*80}")
    print("文件编码支持演示")
    print(f"{'='*80}")
    
    # 包含中文的配置内容
    chinese_content = """# 防火墙配置文件
# 管理员：张三
# 创建时间：2024年1月1日
# 说明：这是一个包含中文注释的配置文件

# 管理访问规则
allow tcp from ************/24 to ***********0 port 22

# Web服务规则  
allow tcp from ***********/24 to ************** port 80

# 违规示例：管理端口开放
allow tcp from any to any port 22
"""
    
    encodings = ['utf-8', 'gbk']
    
    for encoding in encodings:
        print(f"\n测试 {encoding.upper()} 编码:")
        
        # 创建测试文件
        test_file = f"test_chinese_{encoding}.txt"
        
        try:
            with open(test_file, 'w', encoding=encoding) as f:
                f.write(chinese_content)
            
            # 测试读取和分析
            analyzer = NetworkSecurityAnalyzer()
            if analyzer.load_config_from_file(test_file):
                print(f"  ✅ 成功读取 {encoding.upper()} 编码文件")
                print(f"  解析规则: {len(analyzer.rules)} 条")
                
                # 执行合规性检查
                report = analyzer.check_compliance()
                print(f"  合规检查: {report['summary']['total_issues']} 个问题")
            else:
                print(f"  ❌ 读取 {encoding.upper()} 编码文件失败")
            
            # 清理文件
            os.remove(test_file)
            
        except Exception as e:
            print(f"  ❌ {encoding.upper()} 编码测试失败: {e}")


def main():
    """主演示函数"""
    try:
        demo_txt_format_analysis()
        demo_format_comparison()
        demo_encoding_support()
        
        print(f"\n\n{'='*80}")
        print("TXT格式导入演示完成")
        print(f"{'='*80}")
        
        print(f"\n总结:")
        print(f"✅ 支持多种TXT格式自动识别和转换")
        print(f"✅ 支持多种文件编码格式")
        print(f"✅ 提供完整的合规性检查功能")
        print(f"✅ 生成详细的分析报告")
        
        print(f"\n使用建议:")
        print(f"1. 推荐使用UTF-8编码保存TXT配置文件")
        print(f"2. 可以在同一文件中混合使用多种格式")
        print(f"3. 使用注释行说明规则用途和业务需求")
        print(f"4. 定期使用工具检查配置合规性")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
