#!/usr/bin/env python3
"""
网络设备配置安全分析工具演示脚本
展示工具的主要功能和合规性检查能力
"""

from network_security_analyzer import NetworkSecurityAnalyzer
import json


def demo_compliance_check():
    """演示合规性检查功能"""
    print("=" * 80)
    print("网络设备配置安全合规性检查演示")
    print("=" * 80)
    
    # 测试违规配置
    print("\n1. 测试包含违规项的配置文件")
    print("-" * 50)
    
    analyzer = NetworkSecurityAnalyzer()
    
    # 加载违规配置文件
    if analyzer.load_config_from_file("sample_firewall_config.conf"):
        print(f"✓ 成功加载配置文件，解析出 {len(analyzer.rules)} 条规则")
        
        # 执行合规性检查
        report = analyzer.check_compliance()
        
        # 显示检查结果
        print(f"\n合规状态: {report['compliance_status']}")
        print(f"发现问题: {report['summary']['total_issues']} 项")
        print(f"  - 严重违规: {report['summary']['violations']} 项")
        print(f"  - 警告事项: {report['summary']['warnings']} 项")
        
        # 显示前5个问题
        print(f"\n主要问题列表:")
        for i, issue in enumerate(report['issues'][:5], 1):
            level_symbol = "🔴" if issue['level'] == "violation" else "🟡"
            print(f"{i}. {level_symbol} [{issue['category']}] {issue['description']}")
        
        if len(report['issues']) > 5:
            print(f"... 还有 {len(report['issues']) - 5} 个问题")
    else:
        print("✗ 配置文件加载失败")
        return
    
    # 测试合规配置
    print(f"\n\n2. 测试符合规范的配置文件")
    print("-" * 50)
    
    analyzer2 = NetworkSecurityAnalyzer()
    
    # 加载合规配置文件
    if analyzer2.load_config_from_file("compliant_firewall_config.conf"):
        print(f"✓ 成功加载配置文件，解析出 {len(analyzer2.rules)} 条规则")
        
        # 执行合规性检查
        report2 = analyzer2.check_compliance()
        
        # 显示检查结果
        print(f"\n合规状态: {report2['compliance_status']}")
        print(f"发现问题: {report2['summary']['total_issues']} 项")
        
        if report2['issues']:
            print(f"问题列表:")
            for i, issue in enumerate(report2['issues'], 1):
                level_symbol = "🔴" if issue['level'] == "violation" else "🟡"
                print(f"{i}. {level_symbol} [{issue['category']}] {issue['description']}")
        else:
            print("✅ 恭喜！配置完全符合技术规范要求")
    else:
        print("✗ 配置文件加载失败")


def demo_specific_checks():
    """演示特定检查功能"""
    print(f"\n\n3. 特定安全检查演示")
    print("-" * 50)
    
    analyzer = NetworkSecurityAnalyzer()
    
    # 创建测试规则
    test_config = """
# 测试管理端口开放
-A INPUT -p tcp --dport 22 -j ACCEPT
-A INPUT -s ************* -p tcp --dport 22 -j ACCEPT
-A INPUT -p tcp --dport 23 -j ACCEPT
-A INPUT -p tcp --dport 3389 -j ACCEPT

# 测试any IP使用
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -s ***********/24 -d ************* -p tcp --dport 8080 -j ACCEPT

# 测试过于宽泛的访问控制
-A FORWARD -j ACCEPT
"""
    
    if analyzer.parse_config(test_config):
        print(f"✓ 解析测试配置，共 {len(analyzer.rules)} 条规则")
        
        # 执行检查
        report = analyzer.check_compliance()
        
        print(f"\n检查结果分类统计:")
        print(f"  管理端口安全问题: {report['categories']['management_ports']} 项")
        print(f"  IP地址规范问题: {report['categories']['any_ip_usage']} 项")
        print(f"  访问控制问题: {report['categories']['access_control']} 项")
        
        # 按类别显示问题
        mgmt_issues = [issue for issue in report['issues'] if issue['category'] == "管理端口安全"]
        ip_issues = [issue for issue in report['issues'] if issue['category'] == "IP地址规范"]
        access_issues = [issue for issue in report['issues'] if issue['category'] == "访问控制"]
        
        if mgmt_issues:
            print(f"\n管理端口安全问题:")
            for issue in mgmt_issues:
                print(f"  - {issue['description']}")
        
        if ip_issues:
            print(f"\nIP地址规范问题:")
            for issue in ip_issues:
                print(f"  - {issue['description']}")
        
        if access_issues:
            print(f"\n访问控制问题:")
            for issue in access_issues:
                print(f"  - {issue['description']}")


def demo_management_port_analysis():
    """演示管理端口分析"""
    print(f"\n\n4. 管理端口开放情况分析")
    print("-" * 50)
    
    analyzer = NetworkSecurityAnalyzer()
    
    # 显示工具检查的管理端口
    print("工具检查的管理端口列表:")
    for port, service in analyzer.MANAGEMENT_PORTS.items():
        print(f"  {port:5d} - {service}")
    
    # 分析示例配置中的管理端口开放情况
    if analyzer.load_config_from_file("sample_firewall_config.conf"):
        report = analyzer.check_compliance()
        
        mgmt_issues = [issue for issue in report['issues'] if issue['category'] == "管理端口安全"]
        
        print(f"\n在示例配置中发现的管理端口问题:")
        for issue in mgmt_issues:
            if issue['level'] == 'violation':
                print(f"  🔴 严重: {issue['description']}")
            else:
                print(f"  🟡 警告: {issue['description']}")


def demo_best_practices():
    """演示最佳实践建议"""
    print(f"\n\n5. 网络安全最佳实践建议")
    print("-" * 50)
    
    print("根据技术规范要求，建议遵循以下最佳实践:")
    print()
    print("1. 管理端口安全:")
    print("   - 禁止对any IP开放SSH(22)、Telnet(23)、RDP(3389)等管理端口")
    print("   - 管理端口访问应限制在特定的管理网段")
    print("   - 使用VPN或跳板机进行远程管理")
    print()
    print("2. IP地址规范:")
    print("   - 出访和入访目标IP禁止设置为any")
    print("   - 使用具体的IP地址或最小必要的网段")
    print("   - 实施最小权限原则")
    print()
    print("3. 访问控制:")
    print("   - 实施端口级访问控制")
    print("   - DMZ区与内网区之间需要严格的访问控制")
    print("   - 外联单位访问需要明确的业务授权")
    print()
    print("4. 监控和审计:")
    print("   - 启用防火墙日志记录")
    print("   - 定期审查防火墙规则")
    print("   - 监控异常访问行为")


def main():
    """主演示函数"""
    try:
        demo_compliance_check()
        demo_specific_checks()
        demo_management_port_analysis()
        demo_best_practices()
        
        print(f"\n\n" + "=" * 80)
        print("演示完成")
        print("=" * 80)
        print()
        print("使用说明:")
        print("1. 运行合规性检查: python network_security_analyzer.py config.conf --compliance")
        print("2. 生成JSON报告: python network_security_analyzer.py config.conf --compliance --format json")
        print("3. 保存报告到文件: python network_security_analyzer.py config.conf --compliance --output report.txt")
        
    except FileNotFoundError as e:
        print(f"错误：找不到配置文件 - {e}")
        print("请确保示例配置文件存在:")
        print("  - sample_firewall_config.conf")
        print("  - compliant_firewall_config.conf")
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
