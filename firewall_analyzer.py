#!/usr/bin/env python3
"""
网络设备防火墙配置分析工具
功能：
1. 分析防火墙配置规则是否禁止开放
2. 支持多种防火墙配置格式（iptables, Cisco ASA, pfSense等）
"""

import re
import json
import argparse
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from enum import Enum


class RuleAction(Enum):
    ALLOW = "allow"
    DENY = "deny"
    DROP = "drop"
    REJECT = "reject"


class Protocol(Enum):
    TCP = "tcp"
    UDP = "udp"
    ICMP = "icmp"
    ANY = "any"


@dataclass
class FirewallRule:
    """防火墙规则数据结构"""
    rule_id: str
    action: RuleAction
    protocol: Protocol
    source_ip: str
    source_port: str
    dest_ip: str
    dest_port: str
    description: str = ""
    
    def __str__(self):
        return f"Rule {self.rule_id}: {self.action.value} {self.protocol.value} " \
               f"{self.source_ip}:{self.source_port} -> {self.dest_ip}:{self.dest_port}"


class FirewallAnalyzer:
    """防火墙配置分析器"""
    
    def __init__(self):
        self.rules: List[FirewallRule] = []
        self.config_type = None
    
    def load_config_from_file(self, file_path: str) -> bool:
        """从文件加载防火墙配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self.parse_config(content)
        except FileNotFoundError:
            print(f"错误：配置文件 {file_path} 不存在")
            return False
        except Exception as e:
            print(f"错误：读取配置文件失败 - {e}")
            return False
    
    def parse_config(self, config_content: str) -> bool:
        """解析防火墙配置内容"""
        # 检测配置类型
        if "iptables" in config_content.lower() or "-A" in config_content:
            return self._parse_iptables(config_content)
        elif "access-list" in config_content.lower():
            return self._parse_cisco_asa(config_content)
        elif "pass" in config_content.lower() and "block" in config_content.lower():
            return self._parse_pfsense(config_content)
        else:
            print("警告：无法识别防火墙配置格式，尝试通用解析")
            return self._parse_generic(config_content)
    
    def _parse_iptables(self, content: str) -> bool:
        """解析iptables配置"""
        self.config_type = "iptables"
        lines = content.strip().split('\n')
        rule_id = 1
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            # 解析iptables规则
            # 示例: -A INPUT -p tcp --dport 22 -j ACCEPT
            match = re.match(r'-A\s+\w+\s+(.+)', line)
            if match:
                rule_parts = match.group(1)
                rule = self._parse_iptables_rule(str(rule_id), rule_parts)
                if rule:
                    self.rules.append(rule)
                    rule_id += 1
        
        return len(self.rules) > 0
    
    def _parse_iptables_rule(self, rule_id: str, rule_parts: str) -> FirewallRule:
        """解析单条iptables规则"""
        # 默认值
        protocol = Protocol.ANY
        source_ip = "any"
        source_port = "any"
        dest_ip = "any"
        dest_port = "any"
        action = RuleAction.DENY
        
        # 解析协议
        proto_match = re.search(r'-p\s+(\w+)', rule_parts)
        if proto_match:
            proto_str = proto_match.group(1).lower()
            if proto_str in [p.value for p in Protocol]:
                protocol = Protocol(proto_str)
        
        # 解析目标端口
        dport_match = re.search(r'--dport\s+(\d+)', rule_parts)
        if dport_match:
            dest_port = dport_match.group(1)
        
        # 解析源端口
        sport_match = re.search(r'--sport\s+(\d+)', rule_parts)
        if sport_match:
            source_port = sport_match.group(1)
        
        # 解析源IP
        src_match = re.search(r'-s\s+([\d./]+)', rule_parts)
        if src_match:
            source_ip = src_match.group(1)
        
        # 解析目标IP
        dst_match = re.search(r'-d\s+([\d./]+)', rule_parts)
        if dst_match:
            dest_ip = dst_match.group(1)
        
        # 解析动作
        if '-j ACCEPT' in rule_parts:
            action = RuleAction.ALLOW
        elif '-j DROP' in rule_parts:
            action = RuleAction.DROP
        elif '-j REJECT' in rule_parts:
            action = RuleAction.REJECT
        
        return FirewallRule(
            rule_id=rule_id,
            action=action,
            protocol=protocol,
            source_ip=source_ip,
            source_port=source_port,
            dest_ip=dest_ip,
            dest_port=dest_port,
            description=rule_parts
        )
    
    def _parse_cisco_asa(self, content: str) -> bool:
        """解析Cisco ASA配置"""
        self.config_type = "cisco_asa"
        # TODO: 实现Cisco ASA解析逻辑
        print("Cisco ASA配置解析功能待实现")
        return False
    
    def _parse_pfsense(self, content: str) -> bool:
        """解析pfSense配置"""
        self.config_type = "pfsense"
        # TODO: 实现pfSense解析逻辑
        print("pfSense配置解析功能待实现")
        return False
    
    def _parse_generic(self, content: str) -> bool:
        """通用配置解析"""
        self.config_type = "generic"
        # TODO: 实现通用解析逻辑
        print("通用配置解析功能待实现")
        return False
    
    def check_port_access(self, target_ip: str, target_port: int, protocol: str = "tcp") -> Dict[str, Any]:
        """检查指定IP和端口是否被防火墙规则禁止访问"""
        result = {
            "target": f"{target_ip}:{target_port}/{protocol}",
            "is_blocked": False,
            "blocking_rules": [],
            "allowing_rules": [],
            "analysis": ""
        }
        
        for rule in self.rules:
            if self._rule_matches_target(rule, target_ip, target_port, protocol):
                if rule.action in [RuleAction.DENY, RuleAction.DROP, RuleAction.REJECT]:
                    result["blocking_rules"].append(rule)
                elif rule.action == RuleAction.ALLOW:
                    result["allowing_rules"].append(rule)
        
        # 分析结果
        if result["blocking_rules"] and not result["allowing_rules"]:
            result["is_blocked"] = True
            result["analysis"] = "目标被明确禁止访问"
        elif result["allowing_rules"] and not result["blocking_rules"]:
            result["is_blocked"] = False
            result["analysis"] = "目标被明确允许访问"
        elif result["blocking_rules"] and result["allowing_rules"]:
            result["is_blocked"] = True  # 默认拒绝策略
            result["analysis"] = "存在冲突规则，建议检查规则优先级"
        else:
            result["is_blocked"] = True  # 默认拒绝策略
            result["analysis"] = "未找到匹配规则，按默认拒绝策略处理"
        
        return result
    
    def _rule_matches_target(self, rule: FirewallRule, target_ip: str, target_port: int, protocol: str) -> bool:
        """检查规则是否匹配目标"""
        # 检查协议
        if rule.protocol != Protocol.ANY and rule.protocol.value != protocol.lower():
            return False
        
        # 检查目标IP
        if rule.dest_ip != "any" and not self._ip_matches(target_ip, rule.dest_ip):
            return False
        
        # 检查目标端口
        if rule.dest_port != "any" and str(target_port) != rule.dest_port:
            return False
        
        return True
    
    def _ip_matches(self, target_ip: str, rule_ip: str) -> bool:
        """检查IP是否匹配规则（支持CIDR）"""
        if rule_ip == "any" or rule_ip == "0.0.0.0/0":
            return True
        
        # 简单的IP匹配，实际应该使用ipaddress模块进行CIDR匹配
        if "/" in rule_ip:
            # CIDR匹配逻辑待完善
            return target_ip.startswith(rule_ip.split('/')[0].rsplit('.', 1)[0])
        else:
            return target_ip == rule_ip
    
    def get_summary(self) -> Dict[str, Any]:
        """获取防火墙配置摘要"""
        total_rules = len(self.rules)
        allow_rules = len([r for r in self.rules if r.action == RuleAction.ALLOW])
        deny_rules = len([r for r in self.rules if r.action in [RuleAction.DENY, RuleAction.DROP, RuleAction.REJECT]])
        
        return {
            "config_type": self.config_type,
            "total_rules": total_rules,
            "allow_rules": allow_rules,
            "deny_rules": deny_rules,
            "rules": [str(rule) for rule in self.rules]
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="防火墙配置分析工具")
    parser.add_argument("config_file", help="防火墙配置文件路径")
    parser.add_argument("--check-ip", help="检查指定IP的访问权限")
    parser.add_argument("--check-port", type=int, help="检查指定端口的访问权限")
    parser.add_argument("--protocol", default="tcp", help="协议类型 (tcp/udp/icmp)")
    parser.add_argument("--summary", action="store_true", help="显示配置摘要")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = FirewallAnalyzer()
    
    # 加载配置
    if not analyzer.load_config_from_file(args.config_file):
        return 1
    
    # 显示摘要
    if args.summary:
        summary = analyzer.get_summary()
        print(json.dumps(summary, indent=2, ensure_ascii=False))
    
    # 检查特定IP和端口
    if args.check_ip and args.check_port:
        result = analyzer.check_port_access(args.check_ip, args.check_port, args.protocol)
        print(f"\n检查结果：{result['target']}")
        print(f"是否被阻止：{'是' if result['is_blocked'] else '否'}")
        print(f"分析：{result['analysis']}")
        
        if result['blocking_rules']:
            print("\n阻止规则：")
            for rule in result['blocking_rules']:
                print(f"  - {rule}")
        
        if result['allowing_rules']:
            print("\n允许规则：")
            for rule in result['allowing_rules']:
                print(f"  - {rule}")
    
    return 0


if __name__ == "__main__":
    exit(main())
