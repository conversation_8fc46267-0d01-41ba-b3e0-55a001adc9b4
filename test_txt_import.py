#!/usr/bin/env python3
"""
测试txt格式配置文件导入功能
"""

from network_security_analyzer import NetworkSecurityAnalyzer
import os


def test_txt_format_import():
    """测试各种txt格式的导入功能"""
    print("=" * 80)
    print("TXT格式配置文件导入功能测试")
    print("=" * 80)
    
    # 测试文件列表
    test_files = [
        ("sample_config_format1.txt", "通用格式 (allow/deny protocol from source to dest port)"),
        ("sample_config_format2.txt", "箭头格式 (source -> dest:port protocol action)"),
        ("sample_config_format3.txt", "表格格式 (protocol port source dest action)"),
        ("sample_config_format4.txt", "动作在前格式 (action protocol source dest port)")
    ]
    
    for file_name, description in test_files:
        print(f"\n{'='*60}")
        print(f"测试文件: {file_name}")
        print(f"格式描述: {description}")
        print(f"{'='*60}")
        
        if not os.path.exists(file_name):
            print(f"❌ 文件 {file_name} 不存在，跳过测试")
            continue
        
        # 创建分析器
        analyzer = NetworkSecurityAnalyzer()
        
        # 加载配置文件
        print(f"\n1. 加载配置文件...")
        if analyzer.load_config_from_file(file_name):
            print(f"✅ 成功加载配置文件")
            print(f"   解析出 {len(analyzer.rules)} 条规则")
            print(f"   配置类型: {analyzer.config_type}")
        else:
            print(f"❌ 配置文件加载失败")
            continue
        
        # 显示解析的规则
        print(f"\n2. 解析的规则列表:")
        for i, rule in enumerate(analyzer.rules[:5], 1):  # 只显示前5条
            print(f"   {i}. {rule}")
        if len(analyzer.rules) > 5:
            print(f"   ... 还有 {len(analyzer.rules) - 5} 条规则")
        
        # 执行合规性检查
        print(f"\n3. 合规性检查结果:")
        report = analyzer.check_compliance()
        
        print(f"   合规状态: {report['compliance_status']}")
        print(f"   问题总数: {report['summary']['total_issues']}")
        print(f"   严重违规: {report['summary']['violations']} 项")
        print(f"   警告事项: {report['summary']['warnings']} 项")
        
        # 显示主要问题
        if report['issues']:
            print(f"\n4. 主要问题列表:")
            for i, issue in enumerate(report['issues'][:3], 1):  # 只显示前3个问题
                level_symbol = "🔴" if issue['level'] == "violation" else "🟡"
                print(f"   {i}. {level_symbol} [{issue['category']}] {issue['description']}")
            if len(report['issues']) > 3:
                print(f"   ... 还有 {len(report['issues']) - 3} 个问题")
        else:
            print(f"   ✅ 未发现合规性问题")


def test_txt_format_conversion():
    """测试txt格式转换功能"""
    print(f"\n\n{'='*80}")
    print("TXT格式转换功能测试")
    print(f"{'='*80}")
    
    analyzer = NetworkSecurityAnalyzer()
    
    # 测试各种格式的转换
    test_cases = [
        ("allow tcp from ***********/24 to ************* port 80", "通用格式"),
        ("************* -> *************:22 TCP ALLOW", "箭头格式"),
        ("TCP 443 ***********/24 ************** ALLOW", "表格格式"),
        ("ALLOW UDP ***********/24 *********** 53", "动作在前格式"),
        ("************* ************* 22 TCP ALLOW", "简单格式"),
        ("-A INPUT -p tcp --dport 80 -j ACCEPT", "iptables格式"),
        ("access-list 100 permit tcp any any eq 80", "Cisco ACL格式")
    ]
    
    print(f"\n格式转换测试:")
    for i, (rule_text, format_name) in enumerate(test_cases, 1):
        print(f"\n{i}. {format_name}:")
        print(f"   原始: {rule_text}")
        
        # 测试转换
        converted = analyzer._convert_txt_rule_format(rule_text)
        print(f"   转换: {converted}")
        
        # 测试解析
        test_config = f"# 测试配置\n{converted}"
        analyzer_test = NetworkSecurityAnalyzer()
        if analyzer_test.parse_config(test_config):
            if analyzer_test.rules:
                print(f"   解析: ✅ 成功解析为规则")
                print(f"   结果: {analyzer_test.rules[0]}")
            else:
                print(f"   解析: ⚠️ 解析成功但未生成规则")
        else:
            print(f"   解析: ❌ 解析失败")


def test_encoding_support():
    """测试编码支持"""
    print(f"\n\n{'='*80}")
    print("文件编码支持测试")
    print(f"{'='*80}")
    
    # 创建不同编码的测试文件
    test_content = """# 测试配置文件
# 包含中文注释：管理端口配置
allow tcp from ***********/24 to ************* port 80
deny tcp from any to any port 22
"""
    
    encodings = ['utf-8', 'gbk', 'gb2312']
    
    for encoding in encodings:
        test_file = f"test_encoding_{encoding}.txt"
        
        try:
            # 创建测试文件
            with open(test_file, 'w', encoding=encoding) as f:
                f.write(test_content)
            
            print(f"\n测试 {encoding} 编码:")
            
            # 测试读取
            analyzer = NetworkSecurityAnalyzer()
            if analyzer.load_config_from_file(test_file):
                print(f"   ✅ 成功读取 {encoding} 编码文件")
                print(f"   解析出 {len(analyzer.rules)} 条规则")
            else:
                print(f"   ❌ 读取 {encoding} 编码文件失败")
            
            # 清理测试文件
            os.remove(test_file)
            
        except Exception as e:
            print(f"   ❌ {encoding} 编码测试失败: {e}")


def main():
    """主测试函数"""
    try:
        test_txt_format_import()
        test_txt_format_conversion()
        test_encoding_support()
        
        print(f"\n\n{'='*80}")
        print("TXT格式导入功能测试完成")
        print(f"{'='*80}")
        
        print(f"\n支持的TXT格式:")
        print(f"1. 通用格式: allow/deny protocol from source_ip to dest_ip port port_number")
        print(f"2. 箭头格式: source_ip -> dest_ip:port protocol action")
        print(f"3. 表格格式: protocol port source_ip dest_ip action")
        print(f"4. 动作在前: action protocol source_ip dest_ip port")
        print(f"5. 简单格式: source_ip dest_ip port protocol action")
        print(f"6. iptables格式: -A INPUT -p tcp --dport 80 -j ACCEPT")
        print(f"7. Cisco ACL格式: access-list 100 permit tcp any any eq 80")
        
        print(f"\n支持的文件编码:")
        print(f"- UTF-8")
        print(f"- GBK")
        print(f"- GB2312")
        print(f"- ASCII")
        print(f"- Latin-1")
        
        print(f"\n使用方法:")
        print(f"python network_security_analyzer.py your_config.txt --compliance")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
