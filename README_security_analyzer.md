# 网络设备配置安全分析工具

## 工具概述

这是一个专门用于检查防火墙配置是否符合网络安全技术规范的Python工具。工具重点关注以下技术规范要求：

1. **外联单位主机与DMZ区前置主机之间的端口级访问控制**
2. **DMZ区前置主机与内网区外联业务服务器之间的端口级访问控制**
3. **出访和入访目标IP禁止设置为any**
4. **业务需求的访问关系禁止开放默认的22、23、3389等设备管理端口**

## 主要功能

### 1. 管理端口安全检查
- 检测SSH(22)、Telnet(23)、RDP(3389)等管理端口的开放情况
- 识别对any IP开放的管理端口（严重违规）
- 标识管理端口的不当开放（警告）

### 2. IP地址规范检查
- 检测源IP或目标IP设置为any的规则
- 确保访问控制遵循最小权限原则
- 识别过于宽泛的网络访问权限

### 3. 访问控制合规性检查
- 验证端口级访问控制的实施
- 检查DMZ区与内网区之间的访问控制
- 识别过于宽松的转发规则

### 4. 多种配置格式支持
- iptables配置格式
- Cisco ACL配置格式
- 通用规则格式

## 安装和使用

### 系统要求
- Python 3.6+
- 无需额外依赖包

### 文件说明
- `network_security_analyzer.py` - 主程序
- `demo_security_analyzer.py` - 演示脚本
- `test_security_analyzer.py` - 测试脚本
- `sample_firewall_config.conf` - 包含违规项的示例配置
- `compliant_firewall_config.conf` - 符合规范的示例配置

### 基本使用

#### 1. 命令行使用
```bash
# 执行合规性检查
python network_security_analyzer.py sample_firewall_config.conf --compliance

# 生成JSON格式报告
python network_security_analyzer.py sample_firewall_config.conf --compliance --format json

# 保存报告到文件
python network_security_analyzer.py sample_firewall_config.conf --compliance --output report.txt
```

#### 2. 运行演示
```bash
# 运行完整演示
python demo_security_analyzer.py
```

#### 3. 运行测试
```bash
# 运行测试套件
python test_security_analyzer.py
```

## 检查项目详解

### 管理端口安全检查

工具检查以下管理端口：
- **22** - SSH
- **23** - Telnet
- **3389** - RDP (Remote Desktop)
- **21** - FTP
- **161/162** - SNMP
- **443/80** - HTTPS/HTTP Management
- **5900-5902** - VNC

**违规示例：**
```bash
# 严重违规：SSH对any开放
-A INPUT -p tcp --dport 22 -j ACCEPT

# 警告：SSH开放但限制了源IP
-A INPUT -s ************* -p tcp --dport 22 -j ACCEPT
```

**合规示例：**
```bash
# 合规：SSH仅限管理网段
-A INPUT -s ************/24 -d ************ -p tcp --dport 22 -j ACCEPT
```

### IP地址规范检查

**违规示例：**
```bash
# 违规：源IP为any
-A INPUT -p tcp --dport 80 -j ACCEPT

# 违规：目标IP为any
-A INPUT -s ***********/24 -p tcp -j ACCEPT
```

**合规示例：**
```bash
# 合规：明确指定源IP和目标IP
-A INPUT -s ***********/24 -d ************* -p tcp --dport 80 -j ACCEPT
```

### 访问控制检查

**违规示例：**
```bash
# 严重违规：允许任意访问
-A FORWARD -j ACCEPT
```

**合规示例：**
```bash
# 合规：端口级访问控制
-A FORWARD -i dmz0 -o lan0 -s ************** -d ************ -p tcp --dport 3306 -j ACCEPT
```

## 报告解读

### 合规状态
- **COMPLIANT** - 完全符合规范
- **WARNING** - 存在警告项
- **VIOLATION** - 存在严重违规

### 问题级别
- **🔴 VIOLATION** - 严重违规，必须整改
- **🟡 WARNING** - 警告事项，建议检查

### 报告示例
```
网络设备配置安全合规性检查报告
================================================================================

配置类型: iptables
规则总数: 15
合规状态: VIOLATION

问题统计:
  严重违规: 8 项
  警告事项: 2 项
  问题总数: 10 项

分类统计:
  管理端口安全: 6 项
  IP地址规范: 3 项
  访问控制: 1 项

详细问题列表:
--------------------------------------------------------------------------------

1. 🔴 [VIOLATION] 管理端口安全
   规则ID: 2
   问题描述: 规则2违规开放管理端口22(SSH)给any IP
   整改建议: 限制管理端口22的访问源IP，不允许设置为any
```

## 最佳实践建议

### 1. 管理端口安全
- 禁止对any IP开放管理端口
- 使用专用管理网段
- 实施跳板机或VPN访问
- 定期审查管理端口开放情况

### 2. IP地址规范
- 避免使用any IP设置
- 实施最小权限原则
- 使用具体的IP地址或最小必要网段
- 定期审查网络访问权限

### 3. 访问控制
- 实施端口级访问控制
- DMZ区与内网区严格隔离
- 外联访问需要明确业务授权
- 定期审查转发规则

### 4. 监控和审计
- 启用防火墙日志
- 定期执行合规性检查
- 监控异常访问行为
- 建立变更管理流程

## 技术规范对照

| 规范要求 | 检查项目 | 违规后果 |
|---------|---------|---------|
| 端口级访问控制 | 检查规则粒度 | 访问控制过于宽泛 |
| 禁止any IP | 检查IP设置 | 网络暴露风险 |
| 禁止管理端口开放 | 检查22/23/3389端口 | 管理接口暴露 |
| DMZ区访问控制 | 检查转发规则 | 内网安全风险 |

## 扩展功能

### 自定义检查规则
可以通过修改代码添加自定义检查规则：

```python
# 添加自定义管理端口
CUSTOM_MANAGEMENT_PORTS = {
    8080: "Custom Admin",
    9090: "Monitoring"
}

# 添加自定义检查逻辑
def custom_check(self):
    # 实现自定义检查逻辑
    pass
```

### 支持新的配置格式
工具采用模块化设计，可以轻松添加新的配置格式支持：

1. 实现新的解析方法
2. 添加格式检测逻辑
3. 测试新格式功能

## 故障排除

### 常见问题
1. **配置文件无法解析**
   - 检查文件格式是否支持
   - 确认文件编码为UTF-8
   - 查看错误日志信息

2. **检查结果不准确**
   - 确认配置格式正确
   - 检查规则语法
   - 验证IP地址格式

3. **性能问题**
   - 大型配置文件可能需要较长处理时间
   - 建议分段检查大型配置

### 技术支持
如遇到问题，请检查：
1. Python版本兼容性
2. 配置文件格式
3. 错误日志信息
4. 示例配置文件测试

## 总结

这个工具可以帮助您：
- 快速识别防火墙配置中的安全风险
- 确保配置符合技术规范要求
- 提供详细的整改建议
- 支持多种配置格式

建议将此工具集成到您的安全审计流程中，定期检查防火墙配置的合规性。
