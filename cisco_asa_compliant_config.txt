! Cisco ASA防火墙配置示例 - 符合技术规范
! 设备: SU_ECN_FW_1_COMPLIANT
! 配置日期: 2024-01-01
! 说明: 此配置符合网络安全技术规范要求

! ========== 接口配置 ==========
interface GigabitEthernet0/0
 nameif outside
 security-level 0
 ip address *********** *************

interface GigabitEthernet0/1
 nameif dmz
 security-level 50
 ip address ************* *************

interface GigabitEthernet0/2
 nameif inside
 security-level 100
 ip address *********** *************

interface GigabitEthernet0/3
 nameif mgmt
 security-level 100
 ip address ************ *************

! ========== 网络对象定义 ==========
object network DMZ_WEB_SERVER
 host *************0

object network DMZ_APP_SERVER
 host **************

object network INTERNAL_DB_SERVER
 host ***********00

object network INTERNAL_APP_SERVER
 host *************

object network EXTERNAL_PARTNER_1
 host ***********00

object network EXTERNAL_PARTNER_2
 host ***********01

object network MGMT_WORKSTATION
 host ************0

object network MGMT_SERVER
 host *************

! ========== 外联单位到DMZ区的访问控制 ==========
! 符合规范: 端口级访问控制，明确指定源IP和目标IP

! Web服务访问 - 外联单位到DMZ前置主机
access-list acl_outside extended permit tcp host ***********00 host *************0 eq 80
access-list acl_outside extended permit tcp host ***********00 host *************0 eq 443
access-list acl_outside extended permit tcp host ***********01 host ************** eq 80
access-list acl_outside extended permit tcp host ***********01 host ************** eq 443

! API服务访问 - 特定外联单位到特定DMZ服务
access-list acl_outside extended permit tcp host ***********00 host ************** eq 8080
access-list acl_outside extended permit tcp host ***********00 host ************** eq 8443

! ========== DMZ区到内网区的访问控制 ==========
! 符合规范: DMZ前置主机到内网业务服务器的端口级访问控制

! 数据库访问 - DMZ应用服务器到内网数据库
access-list acl_dmz extended permit tcp host *************0 host ***********00 eq 3306
access-list acl_dmz extended permit tcp host ************** host ***********00 eq 3306

! 应用服务访问 - DMZ到内网应用服务器
access-list acl_dmz extended permit tcp host *************0 host ************* eq 8080
access-list acl_dmz extended permit tcp host ************** host ************* eq 8080

! Redis缓存访问
access-list acl_dmz extended permit tcp host *************0 host ***********50 eq 6379

! ========== 管理访问控制 ==========
! 符合规范: 管理端口仅限管理网段访问

! SSH管理访问 - 仅限管理网段
access-list acl_mgmt extended permit tcp host ************0 host *************0 eq 22
access-list acl_mgmt extended permit tcp host ************0 host ************** eq 22
access-list acl_mgmt extended permit tcp host ************* host ***********00 eq 22

! HTTPS管理界面 - 仅限管理工作站
access-list acl_mgmt extended permit tcp host ************0 host *************0 eq 443
access-list acl_mgmt extended permit tcp host ************0 host ************** eq 443

! ========== 系统服务访问 ==========
! 符合规范: 系统服务限制源网段

! DNS查询 - 内网到外部DNS
access-list acl_inside extended permit udp *********** ************* host ******* eq 53
access-list acl_inside extended permit udp *********** ************* host ******* eq 53

! NTP时间同步 - 内网到外部NTP
access-list acl_inside extended permit udp *********** ************* host *********** eq 123

! ========== 监控和日志访问 ==========
! 符合规范: 监控访问限制源IP

! SNMP监控 - 仅限监控服务器
access-list acl_mgmt extended permit udp host ************* host *************0 eq 161
access-list acl_mgmt extended permit udp host ************* host ************** eq 161

! 日志收集 - 限制源网段
access-list acl_inside extended permit tcp *********** ************* host ************* eq 514
access-list acl_dmz extended permit tcp ************* ************* host ************* eq 514

! ========== 备份和维护访问 ==========
! 符合规范: 备份访问限制特定IP

! 备份服务访问
access-list acl_inside extended permit tcp host ***********00 host ************* eq 22
access-list acl_inside extended permit tcp host ************* host ************* eq 873

! ========== 业务相关的特殊访问 ==========
! 符合规范: 特定业务需求的精确访问控制

! 邮件服务访问
access-list acl_inside extended permit tcp *********** ************* host ************0 eq 25
access-list acl_inside extended permit tcp *********** ************* host ************0 eq 587

! 文件传输服务 - 使用SFTP而非FTP
access-list acl_dmz extended permit tcp host *************0 host ************* eq 22

! ========== ICMP控制 ==========
! 符合规范: ICMP访问限制

! 内网ping测试
access-list acl_inside extended permit icmp *********** ************* ************* *************
access-list acl_mgmt extended permit icmp ************ ************* ************* *************

! ========== NAT配置 ==========
object network DMZ_WEB_SERVER
 nat (dmz,outside) static ***********0

object network DMZ_APP_SERVER
 nat (dmz,outside) static ************

! 内网访问外网的NAT
nat (inside,outside) after-auto source dynamic any interface

! ========== 访问组应用 ==========
access-group acl_outside in interface outside
access-group acl_dmz in interface dmz
access-group acl_inside in interface inside
access-group acl_mgmt in interface mgmt

! ========== 安全策略配置 ==========
! 启用连接限制
class-map global-class
 match any

policy-map global-policy
 class global-class
  set connection conn-max 1000
  set connection embryonic-conn-max 100

service-policy global-policy global

! ========== 日志和监控配置 ==========
logging enable
logging buffered informational
logging host inside *************
logging facility 16

! 启用连接日志
access-list acl_outside extended permit tcp host ***********00 host *************0 eq 80 log
access-list acl_outside extended permit tcp host ***********00 host *************0 eq 443 log

! ========== 时间配置 ==========
ntp server *********** source inside

! 配置结束
! 注意: 此配置遵循最小权限原则，所有访问都有明确的源IP、目标IP和端口限制
