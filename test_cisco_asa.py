#!/usr/bin/env python3
"""
Cisco ASA配置解析和分析测试脚本
专门测试您提供的错误配置示例
"""

from network_security_analyzer import NetworkSecurityAnalyzer
import os


def test_your_error_config():
    """测试您提供的错误配置示例"""
    print("=" * 80)
    print("测试您提供的Cisco ASA错误配置示例")
    print("=" * 80)
    
    # 您提供的错误配置
    error_config = """! SU_ECN_FW_1
access-list acl_DMZ extended permit tcp host XX.0.XXX.X host XX.0.XXX.XXX eq 3389
"""
    
    print(f"\n原始配置:")
    print(f"SU_ECN_FW_1")
    print(f"access-list acl_DMZ extended permit tcp host XX.0.XXX.X host XX.0.XXX.XXX eq 3389")
    
    # 创建具体IP的测试配置
    test_config = """! SU_ECN_FW_1
access-list acl_DMZ extended permit tcp host ************* host ************** eq 3389
"""
    
    print(f"\n测试配置 (使用具体IP):")
    print(f"access-list acl_DMZ extended permit tcp host ************* host ************** eq 3389")
    
    analyzer = NetworkSecurityAnalyzer()
    
    print(f"\n解析结果:")
    print("-" * 50)
    
    if analyzer.parse_config(test_config):
        print(f"✅ 成功解析配置")
        print(f"   配置类型: {analyzer.config_type}")
        print(f"   解析规则数: {len(analyzer.rules)}")
        
        # 显示解析的规则
        for i, rule in enumerate(analyzer.rules, 1):
            print(f"   规则{i}: {rule}")
        
        # 执行合规性检查
        print(f"\n合规性检查:")
        print("-" * 50)
        
        report = analyzer.check_compliance()
        
        print(f"合规状态: {report['compliance_status']}")
        print(f"问题总数: {report['summary']['total_issues']}")
        print(f"严重违规: {report['summary']['violations']} 项")
        print(f"警告事项: {report['summary']['warnings']} 项")
        
        # 显示具体问题
        if report['issues']:
            print(f"\n发现的问题:")
            for i, issue in enumerate(report['issues'], 1):
                level_symbol = "🔴" if issue['level'] == "violation" else "🟡"
                print(f"{i}. {level_symbol} [{issue['level'].upper()}] {issue['category']}")
                print(f"   问题: {issue['description']}")
                print(f"   建议: {issue['recommendation']}")
                print()
        
        # 分析这个具体的违规
        print(f"针对您的配置的具体分析:")
        print("-" * 50)
        print(f"🔴 严重违规: 开放RDP管理端口3389")
        print(f"   - 规则允许外部主机(*************)访问内部主机(**************)的RDP端口")
        print(f"   - 违反技术规范: 禁止开放默认的22、23、3389等设备管理端口")
        print(f"   - 安全风险: 可能导致远程桌面暴力破解攻击")
        print(f"   - 整改建议: 删除此规则，使用VPN或跳板机进行远程管理")
        
    else:
        print(f"❌ 配置解析失败")


def test_cisco_asa_files():
    """测试Cisco ASA配置文件"""
    print(f"\n\n{'='*80}")
    print("测试完整的Cisco ASA配置文件")
    print(f"{'='*80}")
    
    test_files = [
        ("cisco_asa_error_config.txt", "包含错误配置的ASA文件"),
        ("cisco_asa_compliant_config.txt", "符合规范的ASA文件")
    ]
    
    for file_name, description in test_files:
        print(f"\n{'-'*60}")
        print(f"测试文件: {file_name}")
        print(f"描述: {description}")
        print(f"{'-'*60}")
        
        if not os.path.exists(file_name):
            print(f"❌ 文件 {file_name} 不存在")
            continue
        
        analyzer = NetworkSecurityAnalyzer()
        
        if analyzer.load_config_from_file(file_name):
            print(f"✅ 成功加载配置文件")
            print(f"   配置类型: {analyzer.config_type}")
            print(f"   解析规则数: {len(analyzer.rules)}")
            
            # 显示前几条规则
            print(f"\n前5条解析的规则:")
            for i, rule in enumerate(analyzer.rules[:5], 1):
                print(f"   {i}. {rule}")
            if len(analyzer.rules) > 5:
                print(f"   ... 还有 {len(analyzer.rules) - 5} 条规则")
            
            # 执行合规性检查
            report = analyzer.check_compliance()
            
            print(f"\n合规性检查结果:")
            print(f"   合规状态: {report['compliance_status']}")
            print(f"   问题总数: {report['summary']['total_issues']}")
            print(f"   严重违规: {report['summary']['violations']} 项")
            print(f"   警告事项: {report['summary']['warnings']} 项")
            
            # 按类别统计
            print(f"\n问题分类:")
            print(f"   管理端口安全: {report['categories']['management_ports']} 项")
            print(f"   IP地址规范: {report['categories']['any_ip_usage']} 项")
            print(f"   访问控制: {report['categories']['access_control']} 项")
            
            # 显示主要问题
            if report['issues']:
                print(f"\n主要问题 (前3项):")
                for i, issue in enumerate(report['issues'][:3], 1):
                    level_symbol = "🔴" if issue['level'] == "violation" else "🟡"
                    print(f"   {i}. {level_symbol} {issue['description']}")
            else:
                print(f"   ✅ 未发现合规性问题")
        else:
            print(f"❌ 配置文件加载失败")


def test_cisco_acl_parsing():
    """测试各种Cisco ACL格式解析"""
    print(f"\n\n{'='*80}")
    print("测试各种Cisco ACL格式解析")
    print(f"{'='*80}")
    
    test_cases = [
        # 您的原始格式
        ("扩展ACL with host", "access-list acl_DMZ extended permit tcp host ************* host ************** eq 3389"),
        
        # 其他常见格式
        ("标准ACL", "access-list 100 permit tcp *********** ********* any eq 80"),
        ("扩展ACL with any", "access-list outside_access_in extended permit tcp any host ************* eq 22"),
        ("扩展ACL with 网段", "access-list inside_access_out extended permit tcp *********** ************* any eq 443"),
        ("端口范围", "access-list acl_dmz extended permit tcp host ************** host ************* range 3306 3307"),
        ("ICMP规则", "access-list acl_outside extended permit icmp any host **************"),
        ("UDP规则", "access-list acl_inside extended permit udp *********** ************* host ******* eq 53"),
    ]
    
    for format_name, rule_text in test_cases:
        print(f"\n{format_name}:")
        print(f"   原始: {rule_text}")
        
        # 创建测试配置
        test_config = f"! 测试配置\n{rule_text}"
        
        analyzer = NetworkSecurityAnalyzer()
        if analyzer.parse_config(test_config):
            if analyzer.rules:
                rule = analyzer.rules[0]
                print(f"   解析: ✅ 成功")
                print(f"   结果: {rule.action.value} {rule.protocol.value} {rule.source_ip}:{rule.source_port} -> {rule.dest_ip}:{rule.dest_port}")
                
                # 检查是否有合规性问题
                report = analyzer.check_compliance()
                if report['issues']:
                    issue = report['issues'][0]
                    level_symbol = "🔴" if issue['level'] == "violation" else "🟡"
                    print(f"   合规: {level_symbol} {issue['description']}")
                else:
                    print(f"   合规: ✅ 符合规范")
            else:
                print(f"   解析: ⚠️ 无规则生成")
        else:
            print(f"   解析: ❌ 失败")


def main():
    """主测试函数"""
    try:
        test_your_error_config()
        test_cisco_asa_files()
        test_cisco_acl_parsing()
        
        print(f"\n\n{'='*80}")
        print("Cisco ASA配置测试完成")
        print(f"{'='*80}")
        
        print(f"\n总结:")
        print(f"✅ 成功解析您提供的错误配置示例")
        print(f"✅ 正确识别RDP端口3389开放的违规行为")
        print(f"✅ 支持多种Cisco ACL格式")
        print(f"✅ 提供详细的合规性分析和整改建议")
        
        print(f"\n针对您的配置的建议:")
        print(f"1. 立即删除开放RDP端口3389的规则")
        print(f"2. 使用VPN或跳板机进行远程管理")
        print(f"3. 如确需远程访问，限制源IP为管理网段")
        print(f"4. 定期审查所有管理端口的开放情况")
        print(f"5. 实施最小权限原则，避免使用any IP")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
