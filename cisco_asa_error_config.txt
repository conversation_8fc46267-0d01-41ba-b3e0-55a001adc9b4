! Cisco ASA防火墙配置示例 - 包含错误配置
! 设备: SU_ECN_FW_1
! 配置日期: 2024-01-01
! 说明: 此配置包含违反技术规范的错误示例

! ========== 接口配置 ==========
interface GigabitEthernet0/0
 nameif outside
 security-level 0
 ip address *********** *************

interface GigabitEthernet0/1
 nameif dmz
 security-level 50
 ip address ************* *************

interface GigabitEthernet0/2
 nameif inside
 security-level 100
 ip address *********** *************

! ========== 网络对象定义 ==========
object network DMZ_SERVER_1
 host **************

object network DMZ_SERVER_2
 host **************

object network INTERNAL_DB
 host ***********00

object network EXTERNAL_PARTNER
 host *************

! ========== 错误配置示例 ==========

! 错误1: 开放RDP管理端口给外部主机 - 严重违规
access-list acl_DMZ extended permit tcp host ************* host ************** eq 3389

! 错误2: SSH端口对any开放 - 严重违规
access-list acl_outside extended permit tcp any host ************** eq 22

! 错误3: Telnet端口开放 - 严重违规
access-list acl_DMZ extended permit tcp host ************* host ************** eq 23

! 错误4: 源IP和目标IP都设置为any - 违规
access-list acl_outside extended permit tcp any any eq 80

! 错误5: FTP管理端口对any开放 - 违规
access-list acl_DMZ extended permit tcp any host ************** eq 21

! 错误6: SNMP端口对any开放 - 违规
access-list acl_outside extended permit udp any host ************** eq 161

! 错误7: VNC端口开放 - 违规
access-list acl_DMZ extended permit tcp host ************* host ************** eq 5900

! 错误8: 过于宽泛的访问控制 - 违规
access-list acl_DMZ extended permit ip any any

! ========== 部分合规配置示例 ==========

! 合规: Web服务访问，限制了源IP和目标IP
access-list acl_outside extended permit tcp host ************* host ************** eq 80
access-list acl_outside extended permit tcp host ************* host ************** eq 443

! 合规: 数据库访问，DMZ到内网的特定访问
access-list acl_DMZ extended permit tcp host ************** host ***********00 eq 3306

! 合规: DNS查询，限制源网段
access-list acl_inside extended permit udp *********** ************* host ******* eq 53

! 警告: SSH访问但限制了源IP（需要确认业务需求）
access-list acl_DMZ extended permit tcp host ************ host ************** eq 22

! 合规: 特定业务端口访问
access-list acl_outside extended permit tcp host ************* host ************** eq 8080

! ========== 更多错误示例 ==========

! 错误9: HTTP管理界面对any开放
access-list acl_outside extended permit tcp any host ************** eq 443

! 错误10: 多个管理端口同时开放
access-list acl_DMZ extended permit tcp host ************* host ************** eq 22
access-list acl_DMZ extended permit tcp host ************* host ************** eq 23
access-list acl_DMZ extended permit tcp host ************* host ************** eq 3389

! 错误11: 使用端口范围包含管理端口
access-list acl_outside extended permit tcp any host ************** range 20 25

! ========== NAT配置 ==========
object network DMZ_SERVER_1
 nat (dmz,outside) static ************

object network DMZ_SERVER_2
 nat (dmz,outside) static ************

! ========== 访问组应用 ==========
access-group acl_outside in interface outside
access-group acl_DMZ in interface dmz
access-group acl_inside in interface inside

! ========== 默认拒绝策略 ==========
! 注意: ASA默认拒绝所有未明确允许的流量

! ========== 日志配置 ==========
logging enable
logging buffered informational
logging host inside *************

! 配置结束
