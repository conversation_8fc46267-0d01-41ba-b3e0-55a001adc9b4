#!/usr/bin/env python3
"""
网络设备配置安全分析工具
专门用于检查防火墙配置是否符合技术规范要求

技术规范要求：
1. 外联单位主机与DMZ区前置主机之间的访问控制
2. DMZ区前置主机与内网区外联业务服务器之间的访问控制
3. 出访和入访目标IP禁止设置为any
4. 禁止开放默认的22、23、3389等设备管理端口
"""

import re
import json
import argparse
from typing import List, Dict, Any, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import ipaddress


class RuleAction(Enum):
    ALLOW = "allow"
    DENY = "deny"
    DROP = "drop"
    REJECT = "reject"


class Protocol(Enum):
    TCP = "tcp"
    UDP = "udp"
    ICMP = "icmp"
    ANY = "any"


class ComplianceLevel(Enum):
    COMPLIANT = "compliant"
    WARNING = "warning"
    VIOLATION = "violation"


@dataclass
class FirewallRule:
    """防火墙规则数据结构"""
    rule_id: str
    action: RuleAction
    protocol: Protocol
    source_ip: str
    source_port: str
    dest_ip: str
    dest_port: str
    interface: str = ""
    description: str = ""
    line_number: int = 0
    
    def __str__(self):
        return f"Rule {self.rule_id}: {self.action.value} {self.protocol.value} " \
               f"{self.source_ip}:{self.source_port} -> {self.dest_ip}:{self.dest_port}"


@dataclass
class ComplianceIssue:
    """合规性问题数据结构"""
    level: ComplianceLevel
    category: str
    rule_id: str
    description: str
    recommendation: str
    rule: FirewallRule = None


class NetworkSecurityAnalyzer:
    """网络安全配置分析器"""
    
    # 默认管理端口列表
    MANAGEMENT_PORTS = {
        22: "SSH",
        23: "Telnet", 
        3389: "RDP",
        21: "FTP",
        161: "SNMP",
        162: "SNMP Trap",
        443: "HTTPS Management",
        80: "HTTP Management",
        5900: "VNC",
        5901: "VNC",
        5902: "VNC"
    }
    
    # 危险的any IP设置
    ANY_IP_PATTERNS = ["any", "0.0.0.0/0", "0.0.0.0", "::/0"]
    
    def __init__(self):
        self.rules: List[FirewallRule] = []
        self.config_type = None
        self.compliance_issues: List[ComplianceIssue] = []
    
    def load_config_from_file(self, file_path: str) -> bool:
        """从文件加载防火墙配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return self.parse_config(content)
        except FileNotFoundError:
            print(f"错误：配置文件 {file_path} 不存在")
            return False
        except Exception as e:
            print(f"错误：读取配置文件失败 - {e}")
            return False
    
    def parse_config(self, config_content: str) -> bool:
        """解析防火墙配置内容"""
        # 清空之前的结果
        self.rules.clear()
        self.compliance_issues.clear()
        
        # 检测配置类型并解析
        if "iptables" in config_content.lower() or "-A" in config_content:
            return self._parse_iptables(config_content)
        elif "access-list" in config_content.lower() or "permit" in config_content.lower():
            return self._parse_cisco_acl(config_content)
        elif "rule" in config_content.lower() and ("allow" in config_content.lower() or "deny" in config_content.lower()):
            return self._parse_generic_rules(config_content)
        else:
            print("警告：无法识别防火墙配置格式，尝试通用解析")
            return self._parse_generic_rules(config_content)
    
    def _parse_iptables(self, content: str) -> bool:
        """解析iptables配置"""
        self.config_type = "iptables"
        lines = content.strip().split('\n')
        rule_id = 1
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#') or line.startswith('-P') or line.startswith('-F'):
                continue
                
            # 解析iptables规则
            match = re.match(r'-A\s+\w+\s+(.+)', line)
            if match:
                rule_parts = match.group(1)
                rule = self._parse_iptables_rule(str(rule_id), rule_parts, line_num)
                if rule:
                    self.rules.append(rule)
                    rule_id += 1
        
        return len(self.rules) > 0
    
    def _parse_iptables_rule(self, rule_id: str, rule_parts: str, line_number: int) -> FirewallRule:
        """解析单条iptables规则"""
        # 默认值
        protocol = Protocol.ANY
        source_ip = "any"
        source_port = "any"
        dest_ip = "any"
        dest_port = "any"
        action = RuleAction.DENY
        interface = ""
        
        # 解析协议
        proto_match = re.search(r'-p\s+(\w+)', rule_parts)
        if proto_match:
            proto_str = proto_match.group(1).lower()
            if proto_str in [p.value for p in Protocol]:
                protocol = Protocol(proto_str)
        
        # 解析接口
        iface_match = re.search(r'-i\s+(\w+)', rule_parts)
        if iface_match:
            interface = iface_match.group(1)
        
        # 解析目标端口
        dport_match = re.search(r'--dport\s+(\d+)', rule_parts)
        if dport_match:
            dest_port = dport_match.group(1)
        
        # 解析端口范围
        dport_range_match = re.search(r'--dport\s+(\d+:\d+)', rule_parts)
        if dport_range_match:
            dest_port = dport_range_match.group(1)
        
        # 解析源端口
        sport_match = re.search(r'--sport\s+(\d+)', rule_parts)
        if sport_match:
            source_port = sport_match.group(1)
        
        # 解析源IP
        src_match = re.search(r'-s\s+([\d./]+)', rule_parts)
        if src_match:
            source_ip = src_match.group(1)
        
        # 解析目标IP
        dst_match = re.search(r'-d\s+([\d./]+)', rule_parts)
        if dst_match:
            dest_ip = dst_match.group(1)
        
        # 解析动作
        if '-j ACCEPT' in rule_parts:
            action = RuleAction.ALLOW
        elif '-j DROP' in rule_parts:
            action = RuleAction.DROP
        elif '-j REJECT' in rule_parts:
            action = RuleAction.REJECT
        
        return FirewallRule(
            rule_id=rule_id,
            action=action,
            protocol=protocol,
            source_ip=source_ip,
            source_port=source_port,
            dest_ip=dest_ip,
            dest_port=dest_port,
            interface=interface,
            description=rule_parts,
            line_number=line_number
        )
    
    def _parse_cisco_acl(self, content: str) -> bool:
        """解析Cisco ACL配置"""
        self.config_type = "cisco_acl"
        lines = content.strip().split('\n')
        rule_id = 1
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('!'):
                continue
            
            # 解析Cisco ACL规则
            # 示例: access-list 100 permit tcp *********** ********* any eq 80
            acl_match = re.match(r'access-list\s+\d+\s+(permit|deny)\s+(.+)', line, re.IGNORECASE)
            if acl_match:
                action_str = acl_match.group(1).lower()
                rule_parts = acl_match.group(2)
                rule = self._parse_cisco_acl_rule(str(rule_id), action_str, rule_parts, line_num)
                if rule:
                    self.rules.append(rule)
                    rule_id += 1
        
        return len(self.rules) > 0
    
    def _parse_cisco_acl_rule(self, rule_id: str, action_str: str, rule_parts: str, line_number: int) -> FirewallRule:
        """解析单条Cisco ACL规则"""
        # 默认值
        protocol = Protocol.ANY
        source_ip = "any"
        source_port = "any"
        dest_ip = "any"
        dest_port = "any"
        action = RuleAction.ALLOW if action_str == "permit" else RuleAction.DENY
        
        parts = rule_parts.split()
        if len(parts) >= 1:
            proto_str = parts[0].lower()
            if proto_str in [p.value for p in Protocol]:
                protocol = Protocol(proto_str)
        
        # 简化的Cisco ACL解析（实际情况更复杂）
        if "any" in rule_parts:
            if parts.index("any") == 1:  # 源IP为any
                source_ip = "any"
            if "any" in parts[2:]:  # 目标IP为any
                dest_ip = "any"
        
        # 查找端口信息
        eq_match = re.search(r'eq\s+(\d+)', rule_parts)
        if eq_match:
            dest_port = eq_match.group(1)
        
        return FirewallRule(
            rule_id=rule_id,
            action=action,
            protocol=protocol,
            source_ip=source_ip,
            source_port=source_port,
            dest_ip=dest_ip,
            dest_port=dest_port,
            description=rule_parts,
            line_number=line_number
        )
    
    def _parse_generic_rules(self, content: str) -> bool:
        """解析通用规则格式"""
        self.config_type = "generic"
        lines = content.strip().split('\n')
        rule_id = 1
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            # 尝试解析通用格式
            # 示例: allow tcp from ***********/24 to any port 80
            rule_match = re.match(r'(allow|deny|drop|reject)\s+(.+)', line, re.IGNORECASE)
            if rule_match:
                action_str = rule_match.group(1).lower()
                rule_parts = rule_match.group(2)
                rule = self._parse_generic_rule(str(rule_id), action_str, rule_parts, line_num)
                if rule:
                    self.rules.append(rule)
                    rule_id += 1
        
        return len(self.rules) > 0
    
    def _parse_generic_rule(self, rule_id: str, action_str: str, rule_parts: str, line_number: int) -> FirewallRule:
        """解析通用格式规则"""
        action_map = {
            "allow": RuleAction.ALLOW,
            "deny": RuleAction.DENY,
            "drop": RuleAction.DROP,
            "reject": RuleAction.REJECT
        }
        
        action = action_map.get(action_str, RuleAction.DENY)
        protocol = Protocol.ANY
        source_ip = "any"
        source_port = "any"
        dest_ip = "any"
        dest_port = "any"
        
        # 简单的通用格式解析
        if "tcp" in rule_parts.lower():
            protocol = Protocol.TCP
        elif "udp" in rule_parts.lower():
            protocol = Protocol.UDP
        elif "icmp" in rule_parts.lower():
            protocol = Protocol.ICMP
        
        # 查找IP地址
        ip_pattern = r'(\d+\.\d+\.\d+\.\d+(?:/\d+)?)'
        ips = re.findall(ip_pattern, rule_parts)
        if len(ips) >= 1:
            source_ip = ips[0]
        if len(ips) >= 2:
            dest_ip = ips[1]
        
        # 查找端口
        port_match = re.search(r'port\s+(\d+)', rule_parts)
        if port_match:
            dest_port = port_match.group(1)
        
        return FirewallRule(
            rule_id=rule_id,
            action=action,
            protocol=protocol,
            source_ip=source_ip,
            source_port=source_port,
            dest_ip=dest_ip,
            dest_port=dest_port,
            description=rule_parts,
            line_number=line_number
        )
    
    def check_compliance(self) -> Dict[str, Any]:
        """检查配置合规性"""
        self.compliance_issues.clear()
        
        # 检查管理端口开放情况
        self._check_management_ports()
        
        # 检查any IP设置
        self._check_any_ip_usage()
        
        # 检查DMZ区访问控制
        self._check_dmz_access_control()
        
        # 生成合规性报告
        return self._generate_compliance_report()
    
    def _check_management_ports(self):
        """检查管理端口开放情况"""
        for rule in self.rules:
            if rule.action == RuleAction.ALLOW:
                # 检查目标端口是否为管理端口
                if rule.dest_port.isdigit():
                    port = int(rule.dest_port)
                    if port in self.MANAGEMENT_PORTS:
                        # 检查是否对any开放
                        if self._is_any_ip(rule.source_ip) or self._is_any_ip(rule.dest_ip):
                            self.compliance_issues.append(ComplianceIssue(
                                level=ComplianceLevel.VIOLATION,
                                category="管理端口安全",
                                rule_id=rule.rule_id,
                                description=f"规则{rule.rule_id}违规开放管理端口{port}({self.MANAGEMENT_PORTS[port]})给any IP",
                                recommendation=f"限制管理端口{port}的访问源IP，不允许设置为any",
                                rule=rule
                            ))
                        else:
                            self.compliance_issues.append(ComplianceIssue(
                                level=ComplianceLevel.WARNING,
                                category="管理端口安全",
                                rule_id=rule.rule_id,
                                description=f"规则{rule.rule_id}开放了管理端口{port}({self.MANAGEMENT_PORTS[port]})",
                                recommendation=f"确认管理端口{port}的开放是否符合业务需求",
                                rule=rule
                            ))
    
    def _check_any_ip_usage(self):
        """检查any IP使用情况"""
        for rule in self.rules:
            if rule.action == RuleAction.ALLOW:
                # 检查源IP
                if self._is_any_ip(rule.source_ip):
                    self.compliance_issues.append(ComplianceIssue(
                        level=ComplianceLevel.VIOLATION,
                        category="IP地址规范",
                        rule_id=rule.rule_id,
                        description=f"规则{rule.rule_id}的源IP设置为any，违反技术规范",
                        recommendation="将源IP设置为具体的IP地址或网段",
                        rule=rule
                    ))
                
                # 检查目标IP
                if self._is_any_ip(rule.dest_ip):
                    self.compliance_issues.append(ComplianceIssue(
                        level=ComplianceLevel.VIOLATION,
                        category="IP地址规范", 
                        rule_id=rule.rule_id,
                        description=f"规则{rule.rule_id}的目标IP设置为any，违反技术规范",
                        recommendation="将目标IP设置为具体的IP地址或网段",
                        rule=rule
                    ))
    
    def _check_dmz_access_control(self):
        """检查DMZ区访问控制"""
        # 这里可以根据具体的网络拓扑进行更详细的检查
        # 目前实现基本的端口级访问控制检查
        
        for rule in self.rules:
            if rule.action == RuleAction.ALLOW:
                # 检查是否有过于宽泛的访问权限
                if (self._is_any_ip(rule.source_ip) and 
                    self._is_any_ip(rule.dest_ip) and 
                    rule.dest_port == "any"):
                    self.compliance_issues.append(ComplianceIssue(
                        level=ComplianceLevel.VIOLATION,
                        category="访问控制",
                        rule_id=rule.rule_id,
                        description=f"规则{rule.rule_id}允许任意源到任意目标的任意端口访问",
                        recommendation="实施端口级访问控制，限制具体的源IP、目标IP和端口",
                        rule=rule
                    ))
    
    def _is_any_ip(self, ip_str: str) -> bool:
        """检查IP是否为any类型"""
        return ip_str.lower() in [pattern.lower() for pattern in self.ANY_IP_PATTERNS]
    
    def _generate_compliance_report(self) -> Dict[str, Any]:
        """生成合规性报告"""
        violations = [issue for issue in self.compliance_issues if issue.level == ComplianceLevel.VIOLATION]
        warnings = [issue for issue in self.compliance_issues if issue.level == ComplianceLevel.WARNING]

        # 统计管理端口开放情况
        mgmt_port_issues = [issue for issue in self.compliance_issues if issue.category == "管理端口安全"]
        any_ip_issues = [issue for issue in self.compliance_issues if issue.category == "IP地址规范"]
        access_control_issues = [issue for issue in self.compliance_issues if issue.category == "访问控制"]

        return {
            "config_type": self.config_type,
            "total_rules": len(self.rules),
            "compliance_status": "VIOLATION" if violations else ("WARNING" if warnings else "COMPLIANT"),
            "summary": {
                "violations": len(violations),
                "warnings": len(warnings),
                "total_issues": len(self.compliance_issues)
            },
            "categories": {
                "management_ports": len(mgmt_port_issues),
                "any_ip_usage": len(any_ip_issues),
                "access_control": len(access_control_issues)
            },
            "issues": [
                {
                    "level": issue.level.value,
                    "category": issue.category,
                    "rule_id": issue.rule_id,
                    "description": issue.description,
                    "recommendation": issue.recommendation,
                    "rule_details": str(issue.rule) if issue.rule else None
                }
                for issue in self.compliance_issues
            ]
        }

    def _format_text_report(self, report: Dict[str, Any]) -> str:
        """格式化文本报告"""
        lines = []
        lines.append("=" * 80)
        lines.append("网络设备配置安全合规性检查报告")
        lines.append("=" * 80)

        # 基本信息
        lines.append(f"\n配置类型: {report['config_type']}")
        lines.append(f"规则总数: {report['total_rules']}")
        lines.append(f"合规状态: {report['compliance_status']}")

        # 问题统计
        lines.append(f"\n问题统计:")
        lines.append(f"  严重违规: {report['summary']['violations']} 项")
        lines.append(f"  警告事项: {report['summary']['warnings']} 项")
        lines.append(f"  问题总数: {report['summary']['total_issues']} 项")

        # 分类统计
        lines.append(f"\n分类统计:")
        lines.append(f"  管理端口安全: {report['categories']['management_ports']} 项")
        lines.append(f"  IP地址规范: {report['categories']['any_ip_usage']} 项")
        lines.append(f"  访问控制: {report['categories']['access_control']} 项")

        # 详细问题列表
        if report['issues']:
            lines.append(f"\n详细问题列表:")
            lines.append("-" * 80)

            for i, issue in enumerate(report['issues'], 1):
                level_symbol = "🔴" if issue['level'] == "violation" else "🟡"
                lines.append(f"\n{i}. {level_symbol} [{issue['level'].upper()}] {issue['category']}")
                lines.append(f"   规则ID: {issue['rule_id']}")
                lines.append(f"   问题描述: {issue['description']}")
                lines.append(f"   整改建议: {issue['recommendation']}")
                if issue['rule_details']:
                    lines.append(f"   规则详情: {issue['rule_details']}")
        else:
            lines.append(f"\n✅ 恭喜！配置符合所有技术规范要求")

        # 技术规范要求说明
        lines.append(f"\n技术规范要求:")
        lines.append("-" * 80)
        lines.append("1. 外联单位主机与DMZ区前置主机之间需要端口级访问控制")
        lines.append("2. DMZ区前置主机与内网区外联业务服务器之间需要端口级访问控制")
        lines.append("3. 出访和入访目标IP禁止设置为any")
        lines.append("4. 业务需求的访问关系禁止开放默认的22、23、3389等设备管理端口")

        lines.append("\n" + "=" * 80)

        return "\n".join(lines)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="网络设备配置安全分析工具")
    parser.add_argument("config_file", help="防火墙配置文件路径")
    parser.add_argument("--compliance", action="store_true", help="执行合规性检查")
    parser.add_argument("--output", help="输出报告到文件")
    parser.add_argument("--format", choices=["json", "text"], default="text", help="输出格式")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = NetworkSecurityAnalyzer()
    
    # 加载配置
    if not analyzer.load_config_from_file(args.config_file):
        return 1
    
    print(f"成功解析配置文件，共 {len(analyzer.rules)} 条规则")
    
    # 执行合规性检查
    if args.compliance:
        report = analyzer.check_compliance()
        
        if args.format == "json":
            output = json.dumps(report, indent=2, ensure_ascii=False)
        else:
            output = analyzer._format_text_report(report)
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(output)
            print(f"报告已保存到: {args.output}")
        else:
            print(output)
    
    return 0


if __name__ == "__main__":
    exit(main())
