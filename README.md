# 网络设备防火墙配置分析工具

这是一个Python开发的防火墙配置分析工具，主要用于分析防火墙配置规则是否禁止特定的网络访问。

## 功能特性

1. **防火墙配置解析**
   - 支持iptables配置格式
   - 支持Cisco ASA配置格式（待实现）
   - 支持pfSense配置格式（待实现）
   - 通用配置格式解析（待实现）

2. **规则分析**
   - 检查特定IP和端口是否被防火墙规则禁止访问
   - 识别冲突规则
   - 提供详细的分析报告

3. **配置摘要**
   - 显示防火墙配置统计信息
   - 列出所有规则的详细信息

## 安装要求

- Python 3.6+
- 无额外依赖包

## 使用方法

### 基本用法

```bash
# 显示配置摘要
python firewall_analyzer.py sample_iptables.conf --summary

# 检查特定IP和端口的访问权限
python firewall_analyzer.py sample_iptables.conf --check-ip ************ --check-port 22

# 检查UDP端口
python firewall_analyzer.py sample_iptables.conf --check-ip ******* --check-port 53 --protocol udp
```

### 命令行参数

- `config_file`: 防火墙配置文件路径（必需）
- `--check-ip IP`: 要检查的目标IP地址
- `--check-port PORT`: 要检查的目标端口号
- `--protocol PROTOCOL`: 协议类型（tcp/udp/icmp，默认为tcp）
- `--summary`: 显示配置摘要

### 示例输出

#### 配置摘要
```json
{
  "config_type": "iptables",
  "total_rules": 12,
  "allow_rules": 8,
  "deny_rules": 4,
  "rules": [
    "Rule 1: allow any lo:any -> any:any",
    "Rule 2: allow tcp any:any -> any:22",
    "Rule 3: allow tcp any:any -> any:80",
    ...
  ]
}
```

#### 访问检查结果
```
检查结果：************:22/tcp
是否被阻止：否
分析：目标被明确允许访问

允许规则：
  - Rule 2: allow tcp any:any -> any:22
```

## 配置文件格式

### iptables格式
工具支持标准的iptables配置文件格式，包括：
- `-A` 规则添加
- `-p` 协议指定
- `--dport` 目标端口
- `--sport` 源端口
- `-s` 源IP地址
- `-d` 目标IP地址
- `-j` 动作（ACCEPT/DROP/REJECT）

### 示例配置
参见 `sample_iptables.conf` 文件。

## 代码结构

- `FirewallRule`: 防火墙规则数据结构
- `FirewallAnalyzer`: 主要的分析器类
- `RuleAction`: 规则动作枚举
- `Protocol`: 协议类型枚举

## 扩展功能

工具设计为可扩展的架构，可以轻松添加对其他防火墙配置格式的支持：

1. 实现新的解析方法（如 `_parse_cisco_asa`）
2. 在 `parse_config` 方法中添加格式检测逻辑
3. 测试新格式的解析功能

## 注意事项

1. 当前版本主要支持iptables格式
2. IP地址匹配功能需要进一步完善CIDR支持
3. 规则优先级分析需要根据具体防火墙类型进行调整
4. 建议在生产环境使用前进行充分测试

## 开发计划

- [ ] 完善Cisco ASA配置解析
- [ ] 添加pfSense配置支持
- [ ] 改进IP地址和CIDR匹配
- [ ] 添加规则优先级分析
- [ ] 支持更多防火墙厂商格式
- [ ] 添加Web界面
- [ ] 支持配置文件导出功能
