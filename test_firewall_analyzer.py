#!/usr/bin/env python3
"""
防火墙分析工具测试脚本
"""

import unittest
import tempfile
import os
from firewall_analyzer import Firewall<PERSON>nalyzer, FirewallRule, RuleAction, Protocol


class TestFirewallAnalyzer(unittest.TestCase):
    """防火墙分析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.analyzer = FirewallAnalyzer()
        
        # 创建测试配置内容
        self.test_iptables_config = """
# 测试iptables配置
-A INPUT -i lo -j ACCEPT
-A INPUT -p tcp --dport 22 -j ACCEPT
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -p tcp --dport 443 -j ACCEPT
-A INPUT -s ************* -p tcp --dport 3306 -j ACCEPT
-A INPUT -p tcp --dport 3306 -j DROP
-A INPUT -p udp --dport 53 -j ACCEPT
-A INPUT -j DROP
"""
    
    def test_parse_iptables_config(self):
        """测试iptables配置解析"""
        result = self.analyzer.parse_config(self.test_iptables_config)
        self.assertTrue(result)
        self.assertEqual(self.analyzer.config_type, "iptables")
        self.assertGreater(len(self.analyzer.rules), 0)
    
    def test_check_allowed_port(self):
        """测试检查允许的端口"""
        self.analyzer.parse_config(self.test_iptables_config)
        
        # 检查SSH端口（应该被允许）
        result = self.analyzer.check_port_access("************", 22, "tcp")
        self.assertFalse(result["is_blocked"])
        self.assertGreater(len(result["allowing_rules"]), 0)
    
    def test_check_blocked_port(self):
        """测试检查被阻止的端口"""
        self.analyzer.parse_config(self.test_iptables_config)
        
        # 检查未配置的端口（应该被阻止）
        result = self.analyzer.check_port_access("************", 8080, "tcp")
        self.assertTrue(result["is_blocked"])
    
    def test_check_mysql_access(self):
        """测试MySQL访问控制"""
        self.analyzer.parse_config(self.test_iptables_config)
        
        # 检查允许的IP访问MySQL
        result = self.analyzer.check_port_access("*************", 3306, "tcp")
        self.assertFalse(result["is_blocked"])
        
        # 检查其他IP访问MySQL（应该被阻止）
        result = self.analyzer.check_port_access("*************", 3306, "tcp")
        self.assertTrue(result["is_blocked"])
    
    def test_get_summary(self):
        """测试获取配置摘要"""
        self.analyzer.parse_config(self.test_iptables_config)
        summary = self.analyzer.get_summary()
        
        self.assertEqual(summary["config_type"], "iptables")
        self.assertGreater(summary["total_rules"], 0)
        self.assertIsInstance(summary["rules"], list)
    
    def test_load_config_from_file(self):
        """测试从文件加载配置"""
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.conf') as f:
            f.write(self.test_iptables_config)
            temp_file = f.name
        
        try:
            result = self.analyzer.load_config_from_file(temp_file)
            self.assertTrue(result)
            self.assertGreater(len(self.analyzer.rules), 0)
        finally:
            os.unlink(temp_file)
    
    def test_firewall_rule_creation(self):
        """测试防火墙规则创建"""
        rule = FirewallRule(
            rule_id="test1",
            action=RuleAction.ALLOW,
            protocol=Protocol.TCP,
            source_ip="***********/24",
            source_port="any",
            dest_ip="any",
            dest_port="80",
            description="Allow HTTP from LAN"
        )
        
        self.assertEqual(rule.rule_id, "test1")
        self.assertEqual(rule.action, RuleAction.ALLOW)
        self.assertEqual(rule.protocol, Protocol.TCP)
        self.assertEqual(rule.dest_port, "80")


def run_manual_tests():
    """运行手动测试"""
    print("=== 防火墙分析工具手动测试 ===\n")
    
    # 创建分析器
    analyzer = FirewallAnalyzer()
    
    # 测试配置
    test_config = """
-A INPUT -i lo -j ACCEPT
-A INPUT -p tcp --dport 22 -j ACCEPT
-A INPUT -p tcp --dport 80 -j ACCEPT
-A INPUT -p tcp --dport 443 -j ACCEPT
-A INPUT -s ************* -p tcp --dport 3306 -j ACCEPT
-A INPUT -p tcp --dport 3306 -j DROP
-A INPUT -p udp --dport 53 -j ACCEPT
-A INPUT -j DROP
"""
    
    print("1. 解析测试配置...")
    if analyzer.parse_config(test_config):
        print("✓ 配置解析成功")
    else:
        print("✗ 配置解析失败")
        return
    
    print("\n2. 获取配置摘要...")
    summary = analyzer.get_summary()
    print(f"配置类型: {summary['config_type']}")
    print(f"总规则数: {summary['total_rules']}")
    print(f"允许规则: {summary['allow_rules']}")
    print(f"拒绝规则: {summary['deny_rules']}")
    
    print("\n3. 测试端口访问检查...")
    
    # 测试SSH访问
    result = analyzer.check_port_access("************", 22, "tcp")
    print(f"SSH (22/tcp) 访问: {'被阻止' if result['is_blocked'] else '允许'}")
    
    # 测试HTTP访问
    result = analyzer.check_port_access("********", 80, "tcp")
    print(f"HTTP (80/tcp) 访问: {'被阻止' if result['is_blocked'] else '允许'}")
    
    # 测试MySQL访问（允许的IP）
    result = analyzer.check_port_access("*************", 3306, "tcp")
    print(f"MySQL (3306/tcp) 从*************: {'被阻止' if result['is_blocked'] else '允许'}")
    
    # 测试MySQL访问（其他IP）
    result = analyzer.check_port_access("*************", 3306, "tcp")
    print(f"MySQL (3306/tcp) 从*************: {'被阻止' if result['is_blocked'] else '允许'}")
    
    # 测试未配置的端口
    result = analyzer.check_port_access("************", 8080, "tcp")
    print(f"未配置端口 (8080/tcp): {'被阻止' if result['is_blocked'] else '允许'}")
    
    print("\n4. 详细规则列表:")
    for i, rule in enumerate(analyzer.rules, 1):
        print(f"  {i}. {rule}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 运行单元测试")
    print("2. 运行手动测试")
    print("3. 运行所有测试")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        unittest.main(argv=[''], exit=False, verbosity=2)
    elif choice == "2":
        run_manual_tests()
    elif choice == "3":
        print("=== 运行单元测试 ===")
        unittest.main(argv=[''], exit=False, verbosity=2)
        print("\n" + "="*50 + "\n")
        run_manual_tests()
    else:
        print("无效选择，运行手动测试...")
        run_manual_tests()
