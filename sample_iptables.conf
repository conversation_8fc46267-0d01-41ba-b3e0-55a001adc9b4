# 示例iptables防火墙配置文件
# 这是一个典型的Linux服务器防火墙配置

# 清空现有规则
-F
-X
-Z

# 设置默认策略
-P INPUT DROP
-P FORWARD DROP
-P OUTPUT ACCEPT

# 允许本地回环
-A INPUT -i lo -j ACCEPT
-A OUTPUT -o lo -j ACCEPT

# 允许已建立的连接
-A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT

# 允许SSH访问（端口22）
-A INPUT -p tcp --dport 22 -j ACCEPT

# 允许HTTP访问（端口80）
-A INPUT -p tcp --dport 80 -j ACCEPT

# 允许HTTPS访问（端口443）
-A INPUT -p tcp --dport 443 -j ACCEPT

# 允许DNS查询
-A INPUT -p udp --dport 53 -j ACCEPT
-A INPUT -p tcp --dport 53 -j ACCEPT

# 允许特定IP访问MySQL（端口3306）
-A INPUT -s ************* -p tcp --dport 3306 -j ACCEPT

# 拒绝其他MySQL连接
-A INPUT -p tcp --dport 3306 -j DROP

# 允许ping（ICMP）
-A INPUT -p icmp --icmp-type echo-request -j ACCEPT

# 限制SSH连接频率（防暴力破解）
-A INPUT -p tcp --dport 22 -m state --state NEW -m recent --set
-A INPUT -p tcp --dport 22 -m state --state NEW -m recent --update --seconds 60 --hitcount 4 -j DROP

# 拒绝所有其他入站连接（默认策略已设置为DROP）

# 记录被拒绝的连接
-A INPUT -m limit --limit 5/min -j LOG --log-prefix "iptables denied: " --log-level 7

# 最终拒绝规则
-A INPUT -j DROP
